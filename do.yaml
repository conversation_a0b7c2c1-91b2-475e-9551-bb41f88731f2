alerts:
- rule: DEPLOYMENT_FAILED
- rule: DOMAIN_FAILED
features:
- buildpack-stack=ubuntu-22
ingress:
  rules:
  - component:
      name: goali-backend
      preserve_path_prefix: true
    match:
      path:
        prefix: /api
  - component:
      name: goali-backend
      preserve_path_prefix: true
    match:
      path:
        prefix: /health
  - component:
      name: goali-backend
      preserve_path_prefix: true
    match:
      path:
        prefix: /admin
  - component:
      name: goali-backend
      preserve_path_prefix: true
    match:
      path:
        prefix: /ws
  - component:
      name: goali-frontend
    match:
      path:
        prefix: /
  - component:
      name: static-files-cpnt
    match:
      path:
        prefix: /static
name: monkfish-app
region: lon
services:
- build_command: echo "Build completed - database operations moved to runtime"
  environment_slug: python
  envs:
  - key: DJANGO_SETTINGS_MODULE
    scope: RUN_AND_BUILD_TIME
    value: config.settings.prod
  - key: DJANGO_SECRET_KEY
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: MISTRAL_API_KEY
    scope: RUN_AND_BUILD_TIME
    type: SECRET

  - key: DJANGO_ADMIN_PASSWORD
    scope: BUILD_TIME
    type: SECRET
  - key: STATIC_ROOT
    scope: RUN_AND_BUILD_TIME
    value: /app/staticfiles
  - key: DJANGO_ALLOWED_HOSTS
    scope: RUN_AND_BUILD_TIME
    value: 127.0.0.1,localhost,monkfish-app-jvgae.ondigitalocean.app
  - key: DATABASE_URL
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: REDIS_URL
    scope: RUN_TIME
    type: SECRET
  github:
    branch: gguine/prod-slow
    deploy_on_push: true
    repo: elgui/goali
  http_port: 8080
  instance_count: 2
  instance_size_slug: apps-s-1vcpu-1gb
  name: goali-backend
  run_command: python manage.py start_production  # Force ASGI/Uvicorn for WebSocket support
  source_dir: backend

workers:
- name: worker
  github:
    repo: elgui/goali
    branch: gguine/prod-slow
    deploy_on_push: true
  source_dir: backend
  environment_slug: python
  run_command: celery -A config worker --loglevel=info --concurrency=2
  instance_count: 1
  instance_size_slug: apps-s-1vcpu-1gb
  envs:
  - key: DJANGO_SETTINGS_MODULE
    scope: RUN_AND_BUILD_TIME
    value: config.settings.prod
  - key: DJANGO_SECRET_KEY
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: MISTRAL_API_KEY
    scope: RUN_AND_BUILD_TIME
    type: SECRET

  - key: DATABASE_URL
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: REDIS_URL
    scope: RUN_AND_BUILD_TIME
    value: rediss://default:<EMAIL>:25061/0
  - key: DJANGO_ALLOWED_HOSTS
    scope: RUN_AND_BUILD_TIME
    value: 127.0.0.1,localhost,monkfish-app-jvgae.ondigitalocean.app

static_sites:
- build_command: npm ci && ./generate-config.sh && npm run build:prod
  envs:
  - key: VITE_SECURITY_REQUIRE_AUTH
    scope: BUILD_TIME
    value: "true"
  - key: VITE_SECURITY_TOKEN_VALIDATION
    scope: BUILD_TIME
    value: "true"
  - key: VITE_SECURITY_SESSION_TIMEOUT
    scope: BUILD_TIME
    value: "1800000"
  github:
    branch: gguine/prod-slow
    deploy_on_push: true
    repo: elgui/goali
  name: goali-frontend
  output_dir: /dist
  source_dir: frontend
- build_command: DJANGO_SETTINGS_MODULE=config.settings.prod python manage.py collectstatic --noinput --clear
  github:
    branch: gguine/prod-slow
    deploy_on_push: true
    repo: elgui/goali
  name: static-files-cpnt
  output_dir: staticfiles
  source_dir: /static
