# Advanced Navigation and Code-Concept Exploration

## Part 1: Building a Semantic Code Navigator

### 1.1 Concept Extraction and Linking

Create `concept_extractor.py`:

```python
import ast
import re
from typing import List, Dict, Set
from graphiti_core import Graphiti
from dataclasses import dataclass

@dataclass
class CodeConcept:
    name: str
    type: str  # 'pattern', 'domain', 'technical', 'business'
    description: str
    related_files: List[str]
    related_entities: List[str]

class ConceptExtractor:
    def __init__(self, graphiti_client: Graphiti):
        self.graphiti = graphiti_client
        self.concept_patterns = {
            'design_patterns': [
                ('singleton', r'class\s+\w*Singleton|_instance\s*=\s*None'),
                ('factory', r'class\s+\w*Factory|create_\w+|build_\w+'),
                ('observer', r'subscribe|notify|observer|listeners'),
                ('strategy', r'class\s+\w*Strategy|execute_strategy'),
            ],
            'architectural': [
                ('mvc', r'Model|View|Controller|templates|views\.py'),
                ('repository', r'Repository|get_by_|find_by_|save\('),
                ('service_layer', r'Service|BusinessLogic|UseCase'),
            ],
            'domain_concepts': [
                ('user_management', r'User|Auth|Login|Permission|Role'),
                ('data_processing', r'Transform|Pipeline|Process|Extract'),
                ('api', r'endpoint|route|request|response|REST|GraphQL'),
            ]
        }
        
    async def extract_concepts_from_code(self, file_path: str, content: str) -> List[CodeConcept]:
        """Extract high-level concepts from code"""
        concepts = []
        
        # Check for design patterns
        for pattern_type, patterns in self.concept_patterns.items():
            for pattern_name, pattern_regex in patterns:
                if re.search(pattern_regex, content, re.IGNORECASE):
                    concepts.append(CodeConcept(
                        name=pattern_name,
                        type=pattern_type,
                        description=f"Found in {file_path}",
                        related_files=[file_path],
                        related_entities=self._extract_related_entities(content)
                    ))
        
        # Extract from docstrings and comments
        docstring_concepts = self._extract_from_docstrings(content)
        concepts.extend(docstring_concepts)
        
        return concepts
    
    def _extract_from_docstrings(self, content: str) -> List[CodeConcept]:
        """Extract concepts from docstrings and comments"""
        concepts = []
        
        # Look for specific keywords in docstrings
        docstring_pattern = r'"""(.*?)"""'
        docstrings = re.findall(docstring_pattern, content, re.DOTALL)
        
        concept_keywords = {
            'business_logic': ['business', 'rule', 'policy', 'workflow'],
            'integration': ['integrate', 'external', 'api', 'service'],
            'data_flow': ['transform', 'process', 'pipeline', 'flow']
        }
        
        for docstring in docstrings:
            for concept_type, keywords in concept_keywords.items():
                if any(keyword in docstring.lower() for keyword in keywords):
                    concepts.append(CodeConcept(
                        name=f"{concept_type}_concept",
                        type='domain',
                        description=docstring[:200],
                        related_files=[],
                        related_entities=[]
                    ))
        
        return concepts
    
    def _extract_related_entities(self, content: str) -> List[str]:
        """Extract class and function names that might be related"""
        entities = []
        
        try:
            tree = ast.parse(content)
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    entities.append(f"class:{node.name}")
                elif isinstance(node, ast.FunctionDef):
                    entities.append(f"function:{node.name}")
        except:
            pass
            
        return entities

    async def create_concept_nodes(self, concepts: List[CodeConcept]):
        """Create concept nodes in Graphiti"""
        for concept in concepts:
            concept_data = {
                'name': concept.name,
                'type': concept.type,
                'description': concept.description,
                'related_files': concept.related_files,
                'related_entities': concept.related_entities
            }
            
            await self.graphiti.add_episode(
                name=f"Concept: {concept.name}",
                episode_body=json.dumps(concept_data),
                source=EpisodeType.json,
                source_description="Extracted code concept",
                group_id=os.getenv('ANALYSIS_GROUP_ID')
            )
```

### 1.2 Semantic Search Interface

Create `semantic_navigator.py`:

```python
from typing import List, Dict, Tuple
import asyncio
from graphiti_core import Graphiti
from graphiti_core.search.search_config_recipes import (
    EDGE_HYBRID_SEARCH_RRF,
    NODE_HYBRID_SEARCH_RRF
)

class SemanticNavigator:
    def __init__(self, graphiti_client: Graphiti, vscode_navigator):
        self.graphiti = graphiti_client
        self.vscode = vscode_navigator
        self.navigation_history = []
        
    async def find_implementation(self, concept: str) -> List[Dict]:
        """Find where a concept is implemented"""
        # Search for concept nodes
        concept_results = await self.graphiti._search(
            query=f"{concept} implementation code",
            config=NODE_HYBRID_SEARCH_RRF,
            group_ids=[os.getenv('ANALYSIS_GROUP_ID')]
        )
        
        implementations = []
        for node in concept_results.nodes:
            # Get related code files
            edge_results = await self.graphiti.search(
                query=f"{node.name} implements contains",
                center_node_uuid=node.uuid
            )
            
            for edge in edge_results:
                if 'file:' in edge.target_node_name:
                    implementations.append({
                        'concept': node.name,
                        'file': edge.target_node_name.replace('file:', ''),
                        'relationship': edge.fact,
                        'confidence': edge.score if hasattr(edge, 'score') else 1.0
                    })
        
        return implementations
    
    async def trace_data_flow(self, start_point: str, end_point: str) -> List[Dict]:
        """Trace how data flows between two points"""
        # Find shortest path in graph
        query = f"""
        MATCH path = shortestPath(
            (start:CodeFunction {{name: '{start_point}'}})-[*]->
            (end:CodeFunction {{name: '{end_point}'}})
        )
        RETURN path
        """
        
        # Execute via Graphiti's driver
        async with self.graphiti.driver.session() as session:
            result = await session.run(query)
            paths = []
            
            async for record in result:
                path = record['path']
                path_nodes = []
                
                for node in path.nodes:
                    path_nodes.append({
                        'name': node['name'],
                        'type': list(node.labels)[0],
                        'file': node.get('file_path', '')
                    })
                
                paths.append({
                    'nodes': path_nodes,
                    'length': len(path.nodes)
                })
        
        return paths
    
    async def find_related_concepts(self, current_file: str) -> List[Dict]:
        """Find concepts related to current file"""
        # Get file node
        file_results = await self.graphiti._search(
            query=f"file {current_file}",
            config=NODE_HYBRID_SEARCH_RRF
        )
        
        if not file_results.nodes:
            return []
        
        file_node = file_results.nodes[0]
        
        # Search for related concepts
        related = await self.graphiti.search(
            query="related concept pattern domain",
            center_node_uuid=file_node.uuid,
            num_results=20
        )
        
        concepts = []
        for edge in related:
            if 'Concept:' in edge.target_node_name:
                concepts.append({
                    'name': edge.target_node_name.replace('Concept:', ''),
                    'relationship': edge.fact,
                    'type': edge.metadata.get('concept_type', 'unknown')
                })
        
        return concepts
    
    def navigate_to_concept(self, concept: Dict):
        """Navigate to a concept implementation in VSCode"""
        self.navigation_history.append(concept)
        
        if concept.get('file'):
            self.vscode.open_in_vscode(concept['file'])
        
    async def get_concept_context(self, concept_name: str) -> Dict:
        """Get full context for a concept"""
        context = {
            'implementations': await self.find_implementation(concept_name),
            'related_patterns': [],
            'dependencies': [],
            'usage_examples': []
        }
        
        # Find related patterns
        pattern_results = await self.graphiti.search(
            query=f"{concept_name} pattern design architecture",
            num_results=10
        )
        
        for edge in pattern_results:
            if 'pattern' in edge.fact.lower():
                context['related_patterns'].append({
                    'pattern': edge.target_node_name,
                    'relationship': edge.fact
                })
        
        return context
```

## Part 2: Interactive Code-Concept Explorer

### 2.1 VSCode Extension Helper

Create `vscode_graphiti_bridge.py`:

```python
import json
import asyncio
from typing import Optional
from pathlib import Path

class VSCodeGraphitiBridge:
    def __init__(self, graphiti_client, navigator):
        self.graphiti = graphiti_client
        self.navigator = navigator
        self.current_file = None
        self.current_position = None
        
    async def on_file_opened(self, file_path: str):
        """Called when a file is opened in VSCode"""
        self.current_file = file_path
        
        # Get related concepts
        concepts = await self.navigator.find_related_concepts(file_path)
        
        # Generate quick picks for VSCode
        return {
            'quickPicks': [
                {
                    'label': f"📦 {c['name']}",
                    'description': c['relationship'],
                    'detail': f"Type: {c['type']}",
                    'data': c
                }
                for c in concepts
            ]
        }
    
    async def on_symbol_hover(self, file_path: str, symbol: str, line: int):
        """Called when hovering over a symbol"""
        # Search for symbol information
        symbol_info = await self.graphiti.search(
            query=f"{symbol} function class method",
            group_ids=[os.getenv('ANALYSIS_GROUP_ID')]
        )
        
        if symbol_info:
            top_result = symbol_info[0]
            return {
                'markdown': f"""
### {symbol}

**Type**: {top_result.source_node_name}

**Description**: {top_result.fact}

**Related to**: {top_result.target_node_name}

[View in Graph](command:graphiti.showInGraph?{symbol})
"""
            }
        
        return None
    
    async def get_code_lenses(self, file_path: str) -> List[Dict]:
        """Generate code lenses for VSCode"""
        # Find all functions/classes in file
        file_content = Path(file_path).read_text()
        
        lenses = []
        
        # Search for each function/class in graph
        tree = ast.parse(file_content)
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.ClassDef)):
                # Check complexity/connections
                connections = await self.graphiti.search(
                    query=f"{node.name} calls uses imports",
                    num_results=50
                )
                
                if len(connections) > 10:
                    lenses.append({
                        'line': node.lineno,
                        'command': {
                            'title': f'🔥 High complexity ({len(connections)} connections)',
                            'command': 'graphiti.showComplexity',
                            'arguments': [node.name]
                        }
                    })
                
                # Check for patterns
                if any(pattern in node.name.lower() for pattern in ['factory', 'singleton', 'observer']):
                    lenses.append({
                        'line': node.lineno,
                        'command': {
                            'title': '🎯 Design Pattern Detected',
                            'command': 'graphiti.showPattern',
                            'arguments': [node.name]
                        }
                    })
        
        return lenses
```

### 2.2 Real-time Navigation Dashboard

Create `navigation_dashboard.html`:

```html
<!DOCTYPE html>
<html>
<head>
    <title>Graphiti Code Navigator</title>
    <script src="https://cdn.jsdelivr.net/npm/vis-network@latest/dist/vis-network.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vis-data@latest/dist/vis-data.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/vis-network@latest/dist/vis-network.min.css" rel="stylesheet">
    <style>
        body { 
            margin: 0; 
            font-family: Arial, sans-serif;
            display: grid;
            grid-template-columns: 300px 1fr 300px;
            height: 100vh;
        }
        
        #sidebar-left {
            background: #f5f5f5;
            padding: 20px;
            overflow-y: auto;
        }
        
        #graph-container {
            position: relative;
        }
        
        #sidebar-right {
            background: #f5f5f5;
            padding: 20px;
            overflow-y: auto;
        }
        
        .concept-card {
            background: white;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .concept-card:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        #search-box {
            width: 100%;
            padding: 10px;
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .code-preview {
            background: #2d2d2d;
            color: #f8f8f2;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
        }
        
        .navigation-breadcrumb {
            padding: 10px;
            background: #e0e0e0;
            margin-bottom: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div id="sidebar-left">
        <h3>Search & Navigate</h3>
        <input type="text" id="search-box" placeholder="Search concepts, code, patterns...">
        
        <div id="search-results"></div>
        
        <h4>Recent Navigation</h4>
        <div id="navigation-history"></div>
        
        <h4>Active Filters</h4>
        <div id="filters">
            <label><input type="checkbox" class="filter" value="patterns"> Design Patterns</label><br>
            <label><input type="checkbox" class="filter" value="concepts"> Domain Concepts</label><br>
            <label><input type="checkbox" class="filter" value="workflows"> Workflows</label><br>
            <label><input type="checkbox" class="filter" value="dependencies"> Dependencies</label>
        </div>
    </div>
    
    <div id="graph-container"></div>
    
    <div id="sidebar-right">
        <h3>Context & Details</h3>
        <div id="navigation-breadcrumb" class="navigation-breadcrumb"></div>
        
        <div id="selected-node-info">
            <p>Select a node to view details</p>
        </div>
        
        <div id="code-preview-container"></div>
        
        <div id="related-items"></div>
    </div>
    
    <script>
        // Initialize vis.js network
        const container = document.getElementById('graph-container');
        const nodes = new vis.DataSet();
        const edges = new vis.DataSet();
        
        const data = { nodes: nodes, edges: edges };
        const options = {
            physics: {
                barnesHut: {
                    gravitationalConstant: -30000,
                    centralGravity: 0.1,
                    springLength: 200
                }
            },
            interaction: {
                hover: true,
                navigationButtons: true,
                keyboard: true
            },
            nodes: {
                shape: 'dot',
                font: { size: 12 }
            },
            edges: {
                arrows: 'to',
                smooth: { type: 'cubicBezier' }
            }
        };
        
        const network = new vis.Network(container, data, options);
        
        // Search functionality
        let searchTimeout;
        document.getElementById('search-box').addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performSearch(e.target.value);
            }, 300);
        });
        
        async function performSearch(query) {
            if (!query) return;
            
            const response = await fetch(`/api/search/${encodeURIComponent(query)}`);
            const results = await response.json();
            
            displaySearchResults(results);
            updateGraph(results);
        }
        
        function displaySearchResults(results) {
            const container = document.getElementById('search-results');
            container.innerHTML = results.map(r => `
                <div class="concept-card" onclick="selectNode('${r.id}')">
                    <strong>${r.name}</strong>
                    <p>${r.description}</p>
                    <small>${r.type}</small>
                </div>
            `).join('');
        }
        
        function updateGraph(searchResults) {
            // Clear existing nodes
            nodes.clear();
            edges.clear();
            
            // Add nodes from search results
            searchResults.forEach((result, index) => {
                nodes.add({
                    id: result.id,
                    label: result.name,
                    group: result.type,
                    x: Math.cos(2 * Math.PI * index / searchResults.length) * 200,
                    y: Math.sin(2 * Math.PI * index / searchResults.length) * 200
                });
            });
            
            // Add edges between related nodes
            searchResults.forEach(result => {
                result.connections?.forEach(conn => {
                    edges.add({
                        from: result.id,
                        to: conn.target,
                        label: conn.relationship
                    });
                });
            });
            
            // Fit network to show all nodes
            setTimeout(() => {
                network.fit();
            }, 100);
        }
        
        // Node selection
        network.on('selectNode', (params) => {
            const nodeId = params.nodes[0];
            selectNode(nodeId);
        });
        
        async function selectNode(nodeId) {
            const node = nodes.get(nodeId);
            
            // Update breadcrumb
            updateBreadcrumb(node);
            
            // Fetch and display node details
            const details = await fetch(`/api/node/${nodeId}`).then(r => r.json());
            displayNodeDetails(details);
            
            // Load code preview if available
            if (details.file_path) {
                loadCodePreview(details.file_path, details.line_number);
            }
            
            // Show related items
            showRelatedItems(details.related);
            
            // Highlight connected nodes
            highlightConnected(nodeId);
        }
        
        function highlightConnected(nodeId) {
            const connectedNodes = network.getConnectedNodes(nodeId);
            const updateArray = [];
            
            nodes.forEach(node => {
                if (connectedNodes.includes(node.id) || node.id === nodeId) {
                    updateArray.push({ id: node.id, color: { background: '#4CAF50' } });
                } else {
                    updateArray.push({ id: node.id, color: { background: '#97C2FC' } });
                }
            });
            
            nodes.update(updateArray);
        }
        
        async function loadCodePreview(filePath, lineNumber) {
            const response = await fetch(`/api/code/${encodeURIComponent(filePath)}/${lineNumber}`);
            const code = await response.text();
            
            document.getElementById('code-preview-container').innerHTML = `
                <h4>Code Preview</h4>
                <div class="code-preview">${highlightCode(code)}</div>
                <button onclick="openInVSCode('${filePath}', ${lineNumber})">Open in VSCode</button>
            `;
        }
        
        function openInVSCode(filePath, lineNumber) {
            fetch('/api/open-vscode', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ file: filePath, line: lineNumber })
            });
        }
        
        // Filter functionality
        document.querySelectorAll('.filter').forEach(filter => {
            filter.addEventListener('change', () => {
                applyFilters();
            });
        });
        
        function applyFilters() {
            const activeFilters = Array.from(document.querySelectorAll('.filter:checked'))
                .map(f => f.value);
            
            const filteredNodes = nodes.get({
                filter: (node) => activeFilters.length === 0 || activeFilters.includes(node.group)
            });
            
            // Update graph with filtered nodes
            const filteredNodeIds = filteredNodes.map(n => n.id);
            const filteredEdges = edges.get({
                filter: (edge) => filteredNodeIds.includes(edge.from) && filteredNodeIds.includes(edge.to)
            });
            
            network.setData({ nodes: filteredNodes, edges: filteredEdges });
        }
        
        // Navigation history
        const navigationHistory = [];
        
        function updateBreadcrumb(node) {
            navigationHistory.push(node);
            if (navigationHistory.length > 5) {
                navigationHistory.shift();
            }
            
            const breadcrumb = navigationHistory.map(n => n.label).join(' > ');
            document.getElementById('navigation-breadcrumb').textContent = breadcrumb;
            
            // Update history sidebar
            const historyHtml = navigationHistory.map((n, i) => `
                <div class="concept-card" onclick="selectNode('${n.id}')">
                    ${n.label}
                </div>
            `).join('');
            
            document.getElementById('navigation-history').innerHTML = historyHtml;
        }
        
        // WebSocket for real-time updates
        // Note: This is example code - update the path to match your actual WebSocket endpoints
        // Available endpoints: ws/game/, ws/admin-tester/, ws/benchmark-dashboard/
        const ws = new WebSocket('ws://localhost:8000/ws/game/');
        
        ws.onmessage = (event) => {
            const update = JSON.parse(event.data);
            
            if (update.type === 'file_changed') {
                // Refresh affected nodes
                performSearch(document.getElementById('search-box').value);
            }
        };
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                document.getElementById('search-box').focus();
            }
            
            if (e.ctrlKey && e.key === 'g') {
                e.preventDefault();
                network.fit();
            }
        });
        
        // Initial load
        performSearch('main');
    </script>
</body>
</html>
```

## Part 3: Concept-Code Synchronization

### 3.1 Bidirectional Navigation

Create `concept_code_sync.py`:

```python
class ConceptCodeSync:
    def __init__(self, graphiti_client, vscode_bridge):
        self.graphiti = graphiti_client
        self.vscode = vscode_bridge
        
    async def sync_from_code_change(self, file_path: str, change_type: str):
        """Update graph when code changes"""
        if change_type == 'modified':
            # Re-analyze file
            analyzer = CodebaseAnalyzer(self.graphiti, CodebaseScanner('.'))
            await analyzer.analyze_file(Path(file_path))
            
            # Update related concepts
            concepts = await self.extract_affected_concepts(file_path)
            await self.update_concept_relationships(concepts)
            
    async def sync_from_graph_exploration(self, selected_nodes: List[str]):
        """Update VSCode view based on graph exploration"""
        files_to_open = []
        
        for node_id in selected_nodes:
            # Get node details
            node_info = await self.get_node_info(node_id)
            
            if node_info.get('file_path'):
                files_to_open.append({
                    'path': node_info['file_path'],
                    'line': node_info.get('line_number', 1),
                    'highlight': node_info.get('symbol_name')
                })
        
        # Open files in VSCode
        for file_info in files_to_open:
            self.vscode.open_in_vscode(
                file_info['path'], 
                file_info['line']
            )
```

This comprehensive guide provides you with:

1. **Understanding** of how Graphiti can visualize codebases
2. **Setup instructions** for your Windows VSCode environment  
3. **Visualization techniques** from basic to advanced
4. **Navigation tools** that connect code and concepts
5. **Real-time synchronization** between your code and knowledge graph

The system allows you to explore your codebase at multiple levels simultaneously, see relationships that aren't obvious from file structure alone, and navigate between implementation details and high-level concepts seamlessly.