# backend/apps/main/llm/service.py

# backend/apps/main/llm/service.py

import json
from apps.main.llm.client import LLMClient
import logging
from typing import Optional, List, Dict, Any

# Removed top-level import: from apps.main.models import LLMConfig

logger = logging.getLogger(__name__)

from .response import LLMResponse

class LLMService:
    """Abstract base class for LLM operations"""
    
    async def chat_completion(self, messages, temperature=0.7, max_tokens=2000, tools=None) -> LLMResponse:
        """
        Send a request to the chat completion API
        
        Args:
            messages: List of message objects with role and content
            temperature: Temperature parameter for generation
            max_tokens: Maximum tokens to generate
            tools: Optional tools for function calling
            
        Returns:
            LLMResponse: Standardized response object
        """
        raise NotImplementedError()
    
    async def parse_json_from_text(self, text):
        """
        Parse a JSON object from text
        
        Args:
            text: Text to parse
            
        Returns:
            dict: Parsed JSON object or None if parsing fails
        """
        try:
            # First try direct JSON parsing
            return json.loads(text)
        except json.JSONDecodeError:
            # Try to extract from markdown code blocks
            import re
            json_match = re.search(r"```(?:json)?\s*([\s\S]*?)\s*```", text)
            if json_match:
                try:
                    return json.loads(json_match.group(1))
                except json.JSONDecodeError:
                    logger.warning("Failed to parse JSON from code block")
            
            return None

import os

class RealLLMClient(LLMService):
    """Real implementation that uses the Mistral LLM client, configured via LLMConfig."""

    def __init__(self, llm_config: Optional['LLMConfig'] = None): # Use string literal for hint
        # Import LLMConfig here
        from apps.main.models import LLMConfig
        if llm_config is None:
            # Try to get default config from DB
            try:
                # Check if we're in an async context and handle appropriately
                import asyncio
                try:
                    # Try to get the current event loop
                    loop = asyncio.get_running_loop()
                    # We're in an async context, so we can't make sync DB calls
                    # Fall back to environment variables immediately
                    logger.info("In async context, using environment variables for LLMConfig fallback")
                    llm_config = None
                except RuntimeError:
                    # No event loop running, safe to make sync DB call
                    try:
                        llm_config = LLMConfig.get_default()
                        if llm_config:
                            logger.info(f"Using default LLMConfig from database: {llm_config.name}")
                        else:
                            logger.info("No default LLMConfig found in database, falling back to environment variables")
                    except Exception as db_error:
                        logger.warning(f"Database error retrieving default LLMConfig: {db_error}, falling back to environment variables")
                        llm_config = None
            except Exception as e:
                logger.error(f"Error retrieving default LLMConfig from DB: {str(e)}")
                # Fallback to environment variables if DB retrieval fails
                llm_config = None

            if llm_config is None:
                # Build default from environment variables
                llm_config = self._build_default_from_env()
                if llm_config is None:
                    raise ValueError("No LLMConfig provided, no default found, and no valid environment variables set for default LLMConfig.")
                else:
                    logger.info(f"Using LLMConfig from environment variables: {llm_config.model_name}")
        else:
            logger.info(f"Using provided LLMConfig: {llm_config.name}")

        self.llm_config = llm_config
        # LLMClient handles API key via env var, we only pass the model name
        self.client = LLMClient(llm_config=llm_config)

    @staticmethod
    def _build_default_from_env() -> 'LLMConfig': # Use string literal for type hint
        """
        Build a default LLMConfig instance from environment variables.
        Returns None if required variables are not set.
        """
        # Import LLMConfig here as well (already present from previous edit, ensuring it's here)
        from apps.main.models import LLMConfig
        model_name = os.getenv("DEFAULT_LLM_MODEL_NAME")
        logger.info(f"🔍 DEBUG: DEFAULT_LLM_MODEL_NAME = '{model_name}'")
        if not model_name:
            logger.warning("🔍 DEBUG: DEFAULT_LLM_MODEL_NAME is empty or None")
            return None
        temperature_str = os.getenv("DEFAULT_LLM_TEMPERATURE")
        logger.info(f"🔍 DEBUG: DEFAULT_LLM_TEMPERATURE = '{temperature_str}'")
        input_token_price_str = os.getenv("DEFAULT_LLM_INPUT_TOKEN_PRICE")
        output_token_price_str = os.getenv("DEFAULT_LLM_OUTPUT_TOKEN_PRICE")

        # Parse optional float values safely
        def parse_float(value):
            try:
                return float(value)
            except (TypeError, ValueError):
                return None

        temperature = parse_float(temperature_str)
        input_token_price = parse_float(input_token_price_str)
        output_token_price = parse_float(output_token_price_str)

        # Create a new LLMConfig instance but do not save to DB
        return LLMConfig(
            name="default_from_env",
            model_name=model_name,
            temperature=temperature,
            input_token_price=input_token_price,
            output_token_price=output_token_price,
            is_default=False
        )

    async def chat_completion(self, messages, temperature=0.7, max_tokens=2000, tools=None) -> LLMResponse:
        """
        Send a request to the chat completion API using the real client.
        Uses temperature from the LLMConfig if available, otherwise defaults to the method argument.
        Args:
            messages: List of message objects with role and content
            temperature: Temperature parameter for generation
            max_tokens: Maximum tokens to generate
            tools: Optional tools for function calling
            
        Returns:
            LLMResponse: Standardized response object
        """
        # Use temperature from config if set, otherwise use the default/passed value
        effective_temperature = self.llm_config.temperature if self.llm_config.temperature is not None else temperature

        try:
            return await self.client.chat_completion(
                messages=messages,
                max_tokens=max_tokens,
                tools=tools
            )
        except Exception as e:
            logger.error(f"Error in LLM chat completion: {str(e)}")
            raise

    async def chat_completion_with_structured_output(
        self,
        messages: List[Dict[str, str]],
        schema: Any,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None
    ) -> LLMResponse:
        """
        Perform a chat completion request with structured JSON output using Mistral's JSON mode.

        Args:
            messages: List of message objects with role and content
            schema: Pydantic schema class for validation (for documentation, actual validation done by caller)
            temperature: Temperature parameter for response generation (0.0-1.0)
            max_tokens: Maximum number of tokens to generate

        Returns:
            LLMResponse object containing the structured JSON response
        """
        # Use temperature from config if set, otherwise use the default/passed value
        effective_temperature = self.llm_config.temperature if self.llm_config.temperature is not None else temperature

        try:
            # Delegate to the underlying client
            return await self.client.chat_completion_with_structured_output(
                messages=messages,
                schema=schema,
                temperature=effective_temperature,
                max_tokens=max_tokens
            )
        except Exception as e:
            logger.error(f"Error in structured LLM chat completion: {str(e)}")
            raise

class MockLLMClient(LLMService):
    """Mock implementation for testing"""
    
    def __init__(self, test_responses=None):
        self.test_responses = test_responses or {}
    
    async def chat_completion(self, messages, temperature=0.7, max_tokens=2000, tools=None):
        """Mock implementation that returns predetermined responses"""
        # Look for a matching predefined response
        user_message = next((msg["content"] for msg in messages if msg["role"] == "user"), "")
        
        for pattern, response in self.test_responses.items():
            if pattern in user_message:
                return response
        
        # Default mock response if no patterns match
        return {"content": "This is a mock LLM response"}
    
    async def parse_json_response(self, response):
        """Mock implementation for JSON parsing"""
        if isinstance(response, dict):
            return response
        return {"content": response}
