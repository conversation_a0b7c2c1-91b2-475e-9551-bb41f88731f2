# backend/apps/main/tasks/wheel_generation_tasks.py

from celery import shared_task
import logging
import asyncio
import json
import time
from typing import Dict, Any, Optional

from apps.main.graphs.wheel_generation_graph import run_wheel_generation_workflow
from apps.user.models import UserProfile
from apps.main.models import HistoryEvent
from django.contrib.contenttypes.models import ContentType
from apps.main.services.progress_tracking_service import ProgressTrackingService, ProgressPriority
from apps.main.services.observability_service import observability, CeleryObserver, EventType, Severity

logger = logging.getLogger(__name__)

@shared_task(bind=True, name="execute_wheel_generation_workflow")
@CeleryObserver.instrument_task("wheel_generation")
def execute_wheel_generation_workflow(self,
                                     user_profile_id: str,
                                     context_packet: Dict[str, Any],
                                     workflow_id: Optional[str] = None):
    """
    Execute the complete wheel generation workflow.
    
    This task orchestrates the multi-agent wheel generation process, which:
    1. Starts with mentor agent gathering context
    2. Routes through multiple specialized agents
    3. Creates a personalized activity wheel
    4. Provides that wheel back to the user
    
    Args:
        user_profile_id: The ID of the user profile
        context_packet: Initial context information from user/system
        workflow_id: Optional workflow ID (will generate new one if not provided)
        
    Returns:
        dict: Wheel generation results including the wheel and agent outputs
    """
    # Initialize variables for cleanup
    progress_tracker = None

    try:
        # Generate workflow ID if not provided
        if not workflow_id:
            workflow_id = str(uuid.uuid4())

        logger.info(f"🎡 [CELERY TASK START] Starting wheel generation workflow {workflow_id} for user {user_profile_id}")
        logger.info(f"📦 [CELERY TASK] Context packet keys: {list(context_packet.keys())}")
        logger.info(f"🔧 [CELERY TASK] Task ID: {self.request.id}")
        logger.info(f"🔧 [CELERY TASK] Task name: {self.request.task}")
        logger.info(f"🔧 [CELERY TASK] Task args: {self.request.args}")
        logger.info(f"🔧 [CELERY TASK] Task kwargs: {self.request.kwargs}")

        # Fix database connection issues in Celery context
        from django.db import connections
        from django.db import connection

        # Comprehensive database connection refresh for Celery workers
        try:
            # Close all existing connections
            connections.close_all()

            # Ensure fresh connection
            connections['default'].ensure_connection()

            # Test the connection with a simple query
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                logger.info(f"✅ Database connection test successful: {result}")

            # Also test Django ORM access
            from apps.user.models import UserProfile
            user_count = UserProfile.objects.count()
            logger.info(f"✅ Django ORM test successful: {user_count} users found")

            logger.info("✅ Database connections refreshed and tested for Celery worker")

        except Exception as db_error:
            logger.error(f"❌ Database connection refresh failed: {db_error}")
            # Try one more time with a different approach
            try:
                from django.db import transaction
                with transaction.atomic():
                    connections.close_all()
                    connections['default'].ensure_connection()
                    logger.info("✅ Database connections refreshed with transaction")
            except Exception as retry_error:
                logger.error(f"❌ Database connection retry failed: {retry_error}")
                raise

        logger.info(f"🔥 [CELERY TASK] Emitting workflow start event for observability")
        # Emit workflow start event for observability
        observability.emit_event(
            EventType.WORKFLOW_START,
            'celery',
            'wheel_generation_workflow',
            metadata={
                'user_profile_id': user_profile_id,
                'task_id': self.request.id,
                'workflow_id': workflow_id
            },
            tags={'workflow_type': 'wheel_generation', 'priority': 'high'}
        )

        logger.info(f"📋 [CELERY TASK] Setting up context packet metadata")
        # Ensure context packet has workflow metadata
        if not context_packet.get("task_type"):
            context_packet["task_type"] = "wheel_generation"

        # Store the Celery task ID for tracking
        context_packet["celery_task_id"] = self.request.id

        logger.info(f"🔄 [CELERY TASK] Creating new event loop for async operations")
        # Initialize progress tracking
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        progress_tracker = None

        try:
            logger.info(f"📊 [CELERY TASK] Getting progress tracking service instance")
            # Get progress tracking service
            progress_service = loop.run_until_complete(ProgressTrackingService.get_instance())

            logger.info(f"📊 [CELERY TASK] Creating progress tracker for task {self.request.id}")
            # Create progress tracker
            progress_tracker = progress_service.create_tracker(
                name="Wheel Generation",
                user_id=str(user_profile_id),
                workflow_type="wheel_generation",
                tracker_id=self.request.id
            )

            logger.info(f"📊 [CELERY TASK] Starting initialization stage")
            # Stage 1: Initialize workflow
            stage_id = progress_tracker.start_stage(
                "initialization",
                "Initializing Workflow",
                "Setting up wheel generation process...",
                ProgressPriority.HIGH
            )

            context_packet["progress_tracker_id"] = progress_tracker.tracker_id
            progress_tracker.update_stage(stage_id, 10, "Workflow metadata prepared")
            progress_tracker.complete_stage(stage_id, "Initialization complete")

            logger.info(f"📊 [CELERY TASK] Starting workflow execution stage")
            # Stage 2: Workflow execution
            stage_id = progress_tracker.start_stage(
                "workflow_execution",
                "Executing Workflow",
                "Running multi-agent wheel generation...",
                ProgressPriority.HIGH
            )

            progress_tracker.update_stage(stage_id, 30, "Starting agent coordination")

            logger.info(f"🚀 [CELERY TASK] Starting workflow execution with retry logic")
            # Run the workflow with progress tracking and connection retry logic
            start_time = time.time()

            # Implement retry logic for database connection issues
            max_retries = 3
            last_error = None

            for attempt in range(max_retries):
                try:
                    logger.info(f"🔄 [CELERY TASK] Workflow execution attempt {attempt + 1}/{max_retries}")
                    # Ensure fresh connections before each attempt
                    if attempt > 0:
                        logger.warning(f"Retrying workflow execution (attempt {attempt + 1}/{max_retries})")
                        connections.close_all()
                        connections['default'].ensure_connection()

                    logger.info(f"🎯 [CELERY TASK] Calling run_wheel_generation_workflow with:")
                    logger.info(f"   - user_profile_id: {user_profile_id}")
                    logger.info(f"   - workflow_id: {workflow_id}")
                    logger.info(f"   - context_packet keys: {list(context_packet.keys())}")

                    result = loop.run_until_complete(
                        run_wheel_generation_workflow(
                            user_profile_id=user_profile_id,
                            context_packet=context_packet,
                            workflow_id=workflow_id
                        )
                    )

                    logger.info(f"✅ [CELERY TASK] Workflow execution completed successfully!")
                    logger.info(f"📊 [CELERY TASK] Result type: {type(result)}")
                    logger.info(f"📊 [CELERY TASK] Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")

                    # If we get here, the workflow succeeded
                    if attempt > 0:
                        logger.info(f"✅ Workflow succeeded on retry attempt {attempt + 1}")
                    break

                except Exception as retry_error:
                    last_error = retry_error
                    error_msg = str(retry_error)
                    logger.error(f"❌ [CELERY TASK] Workflow execution failed on attempt {attempt + 1}: {error_msg}")
                    logger.error(f"❌ [CELERY TASK] Exception type: {type(retry_error).__name__}")

                    # Check if this is a database connection error
                    if "connection already closed" in error_msg or "connection" in error_msg.lower():
                        logger.warning(f"Database connection error on attempt {attempt + 1}: {error_msg}")
                        if attempt < max_retries - 1:
                            logger.info(f"🔄 [CELERY TASK] Will retry after database connection error")
                            continue  # Try again

                    # If it's not a connection error, or we've exhausted retries, re-raise
                    logger.error(f"❌ [CELERY TASK] Non-recoverable error, re-raising: {error_msg}")
                    raise retry_error
            else:
                # If we exhausted all retries, raise the last error
                logger.error(f"❌ [CELERY TASK] Exhausted all {max_retries} retry attempts")
                raise last_error

            execution_time = (time.time() - start_time) * 1000
            logger.info(f"⏱️ [CELERY TASK] Workflow execution completed in {execution_time:.0f}ms")

            progress_tracker.update_stage(stage_id, 85, "Workflow execution complete")
            progress_tracker.complete_stage(
                stage_id,
                f"Workflow completed in {execution_time:.0f}ms",
                {"execution_time_ms": execution_time}
            )

        finally:
            logger.info(f"🔄 [CELERY TASK] Closing event loop")
            loop.close()

        logger.info(f"📊 [CELERY TASK] Starting result processing stage")
        # Stage 3: Result processing and completion
        if progress_tracker:
            stage_id = progress_tracker.start_stage(
                "result_processing",
                "Processing Results",
                "Finalizing wheel data and metrics...",
                ProgressPriority.NORMAL
            )

            progress_tracker.update_stage(stage_id, 95, "Results processed")

            # Add performance metrics to result
            if result and isinstance(result, dict):
                logger.info(f"📊 [CELERY TASK] Adding performance metrics to result")
                result["performance_metrics"] = {
                    "total_execution_time_ms": execution_time,
                    "celery_task_id": self.request.id,
                    "progress_tracker_id": progress_tracker.tracker_id,
                    "workflow_efficiency": 1.0 if execution_time < 30000 else 0.8 if execution_time < 60000 else 0.6
                }

            progress_tracker.complete_stage(stage_id, "Result processing complete")
            progress_tracker.complete_tracker(f"Wheel generation completed successfully in {execution_time:.0f}ms")

        # Emit workflow completion event for observability
        observability.emit_event(
            EventType.WORKFLOW_END,
            'celery',
            'wheel_generation_workflow',
            duration_ms=execution_time,
            metadata={
                'user_profile_id': user_profile_id,
                'task_id': self.request.id,
                'workflow_id': workflow_id,
                'success': True,
                'wheel_items_count': len(result.get('wheel', {}).get('items', [])) if result else 0
            },
            metrics={
                'execution_time_ms': execution_time,
                'wheel_items_generated': float(len(result.get('wheel', {}).get('items', [])) if result else 0)
            }
        )

        # Record workflow completion in history
        try:
            # Get user profile content type
            user_profile = UserProfile.objects.get(id=user_profile_id)
            content_type = ContentType.objects.get_for_model(UserProfile)

            # Extract the wheel from the result
            wheel_data = None
            if hasattr(result, 'wheel') and result.wheel:
                wheel_data = result.wheel
            elif isinstance(result, dict) and 'wheel' in result:
                wheel_data = result['wheel']

            # Create history event with performance metrics
            HistoryEvent.objects.create(
                event_type='wheel_generated',
                content_type=content_type,
                object_id=user_profile_id,
                user_profile=user_profile,
                details={
                    'workflow_id': workflow_id or result.workflow_id if hasattr(result, 'workflow_id') else str(self.request.id),
                    'status': 'completed' if not result.get('error', None) else 'failed',
                    'wheel_summary': {
                        'name': wheel_data.get('name', 'Activity Wheel') if wheel_data else None,
                        'item_count': len(wheel_data.get('items', [])) if wheel_data else 0
                    } if wheel_data else None,
                    'agent_flow': 'wheel_generation_graph',
                    'performance_metrics': {
                        'execution_time_ms': execution_time if 'execution_time' in locals() else None,
                        'progress_tracker_id': progress_tracker.tracker_id if progress_tracker else None
                    }
                }
            )
        except Exception as e:
            logger.error(f"Error recording wheel generation history: {str(e)}")
            # Non-critical error, don't re-raise
            
        logger.info(f"🎯 [CELERY TASK] Preparing final result for return")
        logger.info(f"📊 [CELERY TASK] Result type: {type(result)}")
        logger.info(f"📊 [CELERY TASK] Result has dict method: {hasattr(result, 'dict')}")
        logger.info(f"📊 [CELERY TASK] Result is dict: {isinstance(result, dict)}")

        # Return result, converting to dict if needed
        if hasattr(result, 'dict'):
            final_result = result.dict()
            logger.info(f"✅ [CELERY TASK] Returning result.dict() with keys: {list(final_result.keys())}")
            return final_result
        elif isinstance(result, dict):
            logger.info(f"✅ [CELERY TASK] Returning dict result with keys: {list(result.keys())}")
            return result
        else:
            logger.warning(f"⚠️ [CELERY TASK] Unknown result format, returning error")
            return {"error": "Unknown result format", "completed": False}

    except Exception as e:
        logger.error(f"❌ [CELERY TASK] CRITICAL ERROR executing wheel generation workflow: {str(e)}", exc_info=True)
        logger.error(f"❌ [CELERY TASK] Exception type: {type(e).__name__}")
        logger.error(f"❌ [CELERY TASK] Exception args: {e.args}")

        # Mark progress tracker as errored if available
        if progress_tracker:
            progress_tracker.error_stage(
                "workflow_execution",
                f"Workflow execution failed: {str(e)}",
                {"error_type": type(e).__name__, "error_details": str(e)}
            )

        # Emit workflow error event for observability
        observability.emit_event(
            EventType.ERROR,
            'celery',
            'wheel_generation_workflow',
            severity=Severity.ERROR,
            metadata={
                'user_profile_id': user_profile_id,
                'task_id': self.request.id,
                'workflow_id': workflow_id,
                'error_type': type(e).__name__,
                'error_message': str(e),
                'success': False
            }
        )

        return {
            "error": str(e),
            "workflow_id": workflow_id,
            "completed": False,
            "user_profile_id": user_profile_id,
            "task_type": "wheel_generation",
            "performance_metrics": {
                "celery_task_id": self.request.id,
                "progress_tracker_id": progress_tracker.tracker_id if progress_tracker else None,
                "workflow_efficiency": 0.0
            }
        }