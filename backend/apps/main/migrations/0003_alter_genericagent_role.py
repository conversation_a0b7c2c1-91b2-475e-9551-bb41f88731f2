# Generated by Django 4.2.8 on 2025-03-29 09:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0002_initial'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='genericagent',
            name='role',
            field=models.Char<PERSON><PERSON>(choices=[('mentor', 'Mentor Agent'), ('orchestrator', 'Orchestrator Agent'), ('resource', 'Resource & Capacity Management Agent'), ('engagement', 'Engagement & Pattern Analytics Agent'), ('psychological', 'Psychological Monitoring Agent'), ('strategy', 'Strategy Agent'), ('activity', 'Wheel/Activity Agent'), ('ethical', 'Ethical Oversight Agent'), ('dispatcher', 'Dispatcher Agent')], help_text='The specific role this agent fulfills in the system', max_length=20, unique=True),
        ),
    ]
