"""
API Views for frontend integration
Provides endpoints for debug mode and production authentication
"""

import time
import logging
from django.http import JsonResponse
from django.utils import timezone
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.contrib.auth import authenticate, login
from django.contrib.auth.models import User
from django.conf import settings

logger = logging.getLogger(__name__)
import json
import logging

from apps.user.models import UserProfile
from apps.main.models import LLMConfig

logger = logging.getLogger(__name__)


@method_decorator(csrf_exempt, name='dispatch')
class DebugUsersView(View):
    """
    Debug API endpoint to list all users for frontend debug panel
    Only available in debug mode
    """

    def get(self, request):
        if not settings.DEBUG:
            return JsonResponse({'error': 'Debug endpoints not available in production'}, status=403)

        try:
            users = []
            for profile in UserProfile.objects.select_related('user').all()[:50]:  # Limit to 50 users
                users.append({
                    'id': str(profile.id),
                    'name': profile.profile_name or f"User {profile.id}",
                    'is_real': getattr(profile, 'is_real', False),
                })

            return JsonResponse(users, safe=False)
        except Exception as e:
            logger.error(f"Error fetching users for debug: {e}")
            return JsonResponse({'error': 'Failed to fetch users'}, status=500)

    def post(self, request):
        """Create a new test user profile"""
        if not settings.DEBUG:
            return JsonResponse({'error': 'Debug endpoints not available in production'}, status=403)
 
        try:
            import json
            import uuid
            from apps.user.models import Demographics

            # Parse request data
            data = json.loads(request.body) if request.body else {}
            profile_type = data.get('profile_type', 'german_student')

            # Generate unique username
            timestamp = str(int(time.time()))
            username = f"debug_user_{timestamp}"

            # Create Django auth user
            from django.contrib.auth import get_user_model
            User = get_user_model()
            auth_user = User.objects.create_user(
                username=username,
                email=f"{username}@debug.local",
                first_name="Debug",
                last_name="User"
            )

            # Create UserProfile
            if profile_type == 'german_student':
                profile_name = f"Emma Schmidt (Debug {timestamp[-4:]})"
                user_profile = UserProfile.objects.create(
                    user=auth_user,
                    profile_name=profile_name,
                    is_real=False  # This is a debug profile
                )

                # Create demographics for German 22-year-old female student
                Demographics.objects.create(
                    user_profile=user_profile,
                    full_name=profile_name,
                    age=22,
                    gender="Female",
                    location="Berlin, Germany",
                    language="German, English",
                    occupation="Computer Science Student"
                )
            else:
                # Default profile
                profile_name = f"Debug User {timestamp[-4:]}"
                user_profile = UserProfile.objects.create(
                    user=auth_user,
                    profile_name=profile_name,
                    is_real=False
                )

            logger.info(f"Created debug user profile: {profile_name} (ID: {user_profile.id})")

            return JsonResponse({
                'success': True,
                'user': {
                    'id': str(user_profile.id),
                    'name': user_profile.profile_name,
                    'is_real': False,
                },
                'message': f'Created new debug user: {profile_name}'
            })

        except Exception as e:
            logger.error(f"Error creating debug user: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return JsonResponse({'error': f'Failed to create user: {str(e)}'}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class DebugUserDetailView(View):
    """
    Debug API endpoint to get detailed user information
    Only available in debug mode
    """

    def get(self, request, user_id):
        if not settings.DEBUG:
            return JsonResponse({'error': 'Debug endpoints not available in production'}, status=403)

        try:
            from apps.user.models import Demographics, Preference

            # Get user profile
            user_profile = UserProfile.objects.select_related('user').get(id=user_id)

            # Get demographics
            demographics = None
            try:
                demo = Demographics.objects.get(user_profile=user_profile)
                demographics = {
                    'full_name': demo.full_name,
                    'age': demo.age,
                    'gender': demo.gender,
                    'location': demo.location,
                    'language': demo.language,
                    'occupation': demo.occupation,
                }
            except Demographics.DoesNotExist:
                pass

            # Get preferences
            preferences = []
            for pref in Preference.objects.filter(user_profile=user_profile):
                preferences.append({
                    'name': pref.pref_name,
                    'description': pref.pref_description,
                    'strength': pref.pref_strength,
                })

            # Calculate profile completion percentage
            profile_completion = 0
            if demographics:
                profile_completion += 25  # Demographics
            if preferences:
                profile_completion += min(75, len(preferences) * 15)  # Preferences (up to 75%)

            user_details = {
                'id': str(user_profile.id),
                'profile_name': user_profile.profile_name,
                'is_real': getattr(user_profile, 'is_real', False),
                'profile_completion_percentage': profile_completion,
                'demographics': demographics,
                'preferences': preferences,
                'created_at': user_profile.created_at.isoformat() if hasattr(user_profile, 'created_at') else None,
            }

            return JsonResponse(user_details)

        except UserProfile.DoesNotExist:
            return JsonResponse({'error': 'User not found'}, status=404)
        except Exception as e:
            logger.error(f"Error fetching user details for {user_id}: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return JsonResponse({'error': f'Failed to fetch user details: {str(e)}'}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class DebugLLMConfigsView(View):
    """
    Debug API endpoint to list all LLM configurations for frontend debug panel
    Only available in debug mode
    """
    
    def get(self, request):
        if not settings.DEBUG:
            return JsonResponse({'error': 'Debug endpoints not available in production'}, status=403)
        
        try:
            configs = []
            for config in LLMConfig.objects.all():
                configs.append({
                    'id': str(config.id),
                    'name': config.name,
                    'model_name': config.model_name,
                    'temperature': float(config.temperature),
                    'is_default': config.is_default,
                })
            
            return JsonResponse(configs, safe=False)
        except Exception as e:
            logger.error(f"Error fetching LLM configs for debug: {e}")
            return JsonResponse({'error': 'Failed to fetch LLM configs'}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class AuthLoginView(View):
    """
    Authentication endpoint for production mode
    """
    
    def post(self, request):
        try:
            data = json.loads(request.body)
            username = data.get('username')
            password = data.get('password')
            
            if not username or not password:
                return JsonResponse({'error': 'Username and password required'}, status=400)
            
            user = authenticate(request, username=username, password=password)
            if user is not None:
                login(request, user)
                
                # Get user profile
                try:
                    profile = UserProfile.objects.get(user=user)
                    user_data = {
                        'id': str(profile.id),
                        'name': profile.profile_name or user.username,
                        'username': user.username,
                        'email': user.email,
                        'is_staff': user.is_staff,
                    }
                except UserProfile.DoesNotExist:
                    user_data = {
                        'id': str(user.id),
                        'name': user.username,
                        'username': user.username,
                        'email': user.email,
                        'is_staff': user.is_staff,
                    }
                
                return JsonResponse({
                    'success': True,
                    'token': 'session-based',  # Using Django sessions
                    'expires_in': 3600,  # 1 hour
                    'user': user_data,
                    'permissions': ['user']
                })
            else:
                return JsonResponse({'error': 'Invalid credentials'}, status=401)
                
        except json.JSONDecodeError:
            return JsonResponse({'error': 'Invalid JSON'}, status=400)
        except Exception as e:
            logger.error(f"Login error: {e}")
            return JsonResponse({'error': 'Login failed'}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class AuthVerifyView(View):
    """
    Token verification endpoint for production mode
    """
    
    def post(self, request):
        if request.user.is_authenticated:
            try:
                profile = UserProfile.objects.get(user=request.user)
                user_data = {
                    'id': str(profile.id),
                    'name': profile.profile_name or request.user.username,
                    'username': request.user.username,
                    'email': request.user.email,
                    'is_staff': request.user.is_staff,
                }
            except UserProfile.DoesNotExist:
                user_data = {
                    'id': str(request.user.id),
                    'name': request.user.username,
                    'username': request.user.username,
                    'email': request.user.email,
                    'is_staff': request.user.is_staff,
                }
            
            return JsonResponse({
                'valid': True,
                'expires_in': 3600,
                'user': user_data,
                'permissions': ['user']
            })
        else:
            return JsonResponse({'valid': False}, status=401)


@method_decorator(csrf_exempt, name='dispatch')
class AuthRefreshView(View):
    """
    Session refresh endpoint for production mode
    For session-based auth, this just verifies the session is still valid
    """

    def post(self, request):
        if request.user.is_authenticated:
            try:
                profile = UserProfile.objects.get(user=request.user)
                user_data = {
                    'id': str(profile.id),
                    'name': profile.profile_name or request.user.username,
                    'username': request.user.username,
                    'email': request.user.email,
                    'is_staff': request.user.is_staff,
                }
            except UserProfile.DoesNotExist:
                user_data = {
                    'id': str(request.user.id),
                    'name': request.user.username,
                    'username': request.user.username,
                    'email': request.user.email,
                    'is_staff': request.user.is_staff,
                }

            return JsonResponse({
                'success': True,
                'expires_in': 3600,  # 1 hour
                'user': user_data,
                'permissions': ['user']
            })
        else:
            return JsonResponse({'error': 'Not authenticated'}, status=401)


@method_decorator(csrf_exempt, name='dispatch')
class AuthLogoutView(View):
    """
    Logout endpoint for production mode
    """

    def post(self, request):
        from django.contrib.auth import logout
        logout(request)
        return JsonResponse({'success': True})


@method_decorator(csrf_exempt, name='dispatch')
class ActivityCatalogView(View):
    """
    API endpoint to load activity catalog (generic and tailored activities)
    """

    def get(self, request):
        try:
            from apps.activity.models import GenericActivity, ActivityTailored
            from apps.user.models import UserProfile
            from django.db.models import Q
            from django.conf import settings

            # Get search and filter parameters
            search_query = request.GET.get('search', '').strip()
            limit = int(request.GET.get('limit', 30))
            offset = int(request.GET.get('offset', 0))

            # Get user context parameters for smart filtering
            energy_level = request.GET.get('energy_level', '')  # 0-100
            time_available = request.GET.get('time_available', '')  # in minutes
            domain_filter = request.GET.get('domain', '')  # specific domain filter

            # Get user profile if authenticated
            user_profile = None
            if request.user.is_authenticated:
                try:
                    user_profile = UserProfile.objects.get(user=request.user)
                except UserProfile.DoesNotExist:
                    pass

            # Enhanced search filter - search across multiple fields
            search_filter = Q()
            if search_query:
                search_filter = (
                    Q(name__icontains=search_query) |
                    Q(description__icontains=search_query) |
                    Q(instructions__icontains=search_query)
                )

            # Load generic activities with enhanced search and filtering
            generic_activities = []
            generic_queryset = GenericActivity.objects.all()

            # Apply search filter if provided
            if search_query:
                generic_queryset = generic_queryset.filter(search_filter)

            # Apply domain filter if provided
            if domain_filter:
                # For generic activities, we'll use a simple mapping
                # In a real implementation, you'd have proper domain relationships
                pass  # Skip domain filtering for generic activities for now

            # Import the centralized color function
            from apps.main.agents.tools.activity_tools import _get_activity_color

            for i, activity in enumerate(generic_queryset[offset:offset+limit//2]):
                # Determine domain and color using centralized system
                domain = getattr(activity, 'domain', 'general')  # Get domain from activity or default
                color = _get_activity_color(domain, i)  # Use centralized color function with index

                # Calculate relevance score based on user context
                relevance_score = self._calculate_activity_relevance(
                    activity, energy_level, time_available, user_profile
                )

                generic_activities.append({
                    'id': f'generic-{activity.id}',
                    'name': activity.name,
                    'description': activity.description,
                    'domain': domain,
                    'color': color,
                    'base_challenge_rating': 50,  # Default challenge rating
                    'type': 'generic',
                    'icon': '🎯',
                    'relevance_score': relevance_score
                })

            # Load tailored activities for the user with enhanced search and filtering
            tailored_activities = []
            if user_profile:
                tailored_queryset = ActivityTailored.objects.for_user(user_profile)

                # Apply search filter if provided
                if search_query:
                    tailored_queryset = tailored_queryset.filter(search_filter)

                for i, activity in enumerate(tailored_queryset[offset:offset+limit//2]):
                    # Determine domain and color for tailored activities
                    domain = getattr(activity, 'domain', 'personal_growth')  # Get domain from activity or default to personal_growth
                    color = _get_activity_color(domain, i)  # Use centralized color function with index

                    # Calculate relevance score
                    relevance_score = self._calculate_activity_relevance(
                        activity, energy_level, time_available, user_profile
                    )

                    tailored_activities.append({
                        'id': f'tailored-{activity.id}',
                        'name': activity.name,
                        'description': activity.description,
                        'domain': domain,
                        'color': color,
                        'base_challenge_rating': activity.base_challenge_rating,
                        'type': 'tailored',
                        'icon': '⭐' if activity.created_by is None else '👤',
                        'relevance_score': relevance_score
                    })

            # Combine and sort activities by relevance score (tailored first, then by score)
            all_activities = tailored_activities + generic_activities

            # Sort by type (tailored first) and then by relevance score
            all_activities.sort(key=lambda x: (x['type'] != 'tailored', -x['relevance_score']))

            return JsonResponse({
                'success': True,
                'activities': all_activities,
                'total_count': len(all_activities),
                'tailored_count': len(tailored_activities),
                'generic_count': len(generic_activities),
                'search_query': search_query,
                'filters_applied': {
                    'energy_level': energy_level,
                    'time_available': time_available,
                    'domain': domain_filter
                },
                'domain_colors': getattr(settings, 'ACTIVITY_DOMAIN_COLORS', {}),
                'has_more': len(all_activities) == limit
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': str(e),
                'activities': []
            }, status=500)

    def _calculate_activity_relevance(self, activity, energy_level, time_available, user_profile):
        """
        Calculate relevance score for an activity based on user context
        Returns a score from 0-100
        """
        score = 50  # Base score

        try:
            # Energy level matching
            if energy_level:
                energy = int(energy_level)
                activity_energy_requirement = getattr(activity, 'base_challenge_rating', 50)

                # Activities with challenge rating close to user's energy level get higher scores
                energy_diff = abs(energy - activity_energy_requirement)
                energy_bonus = max(0, 30 - energy_diff * 0.6)  # Up to 30 points bonus
                score += energy_bonus

            # Time availability matching
            if time_available:
                time_mins = int(time_available)
                # Parse activity duration (simplified)
                activity_duration = self._parse_duration(getattr(activity, 'duration_range', '15-30 minutes'))

                if activity_duration:
                    # Activities that fit within available time get bonus
                    if activity_duration <= time_mins:
                        score += 20  # Fits within time
                    elif activity_duration <= time_mins * 1.2:
                        score += 10  # Close fit
                    else:
                        score -= 10  # Too long

            # User profile matching (if available)
            if user_profile and hasattr(activity, 'user_profile') and activity.user_profile == user_profile:
                score += 25  # Personalized for this user

        except (ValueError, AttributeError):
            pass  # Keep base score if parsing fails

        return max(0, min(100, score))  # Clamp to 0-100 range

    def _parse_duration(self, duration_str):
        """
        Parse duration string like '15-30 minutes' and return average duration in minutes
        """
        try:
            if 'minute' in duration_str.lower():
                # Extract numbers from string like "15-30 minutes"
                import re
                numbers = re.findall(r'\d+', duration_str)
                if len(numbers) >= 2:
                    return (int(numbers[0]) + int(numbers[1])) / 2
                elif len(numbers) == 1:
                    return int(numbers[0])
            return None
        except:
            return None


# IngressDispatcherView removed - using standard Django URL routing instead


@method_decorator(csrf_exempt, name='dispatch')
class APIRootView(View):
    """
    API root endpoint that lists available API endpoints
    """

    def get(self, request):
        """Return list of available API endpoints"""
        api_endpoints = {
            'message': 'Goali API - Available Endpoints',
            'version': '1.0.0',
            'endpoints': {
                'health': '/api/health/',
                'authentication': {
                    'login': '/api/auth/login/',
                    'verify': '/api/auth/verify/',
                    'refresh': '/api/auth/refresh/',
                    'logout': '/api/auth/logout/',
                    'beta_signup': '/api/auth/beta-signup/'
                },
                'activities': {
                    'catalog': '/api/activities/catalog/',
                    'create': '/api/activities/create/',
                    'tailor': '/api/activities/tailor/'
                },
                'user': {
                    'profile': '/api/user/profile/',
                    'feedback': '/api/feedback/'
                },
                'wheel': {
                    'items': '/api/wheel-items/',
                    'item_detail': '/api/wheel-items/{wheel_item_id}/'
                },
                'activity_colors': {
                    'config': '/api/activity-colors/config/',
                    'by_domain': '/api/activity-colors/domain/{domain}/'
                }
            },
            'debug_endpoints': {
                'note': 'Available only in debug mode',
                'users': '/api/debug/users/',
                'user_detail': '/api/debug/users/{user_id}/',
                'llm_configs': '/api/debug/llm-configs/'
            }
        }

        return JsonResponse(api_endpoints)


@method_decorator(csrf_exempt, name='dispatch')
class HealthCheckView(View):
    """
    Health check endpoint for frontend to verify backend availability
    """

    def get(self, request):
        try:
            # Debug: Log request headers to understand ingress routing
            logger.info(f"Health check request headers: {dict(request.headers)}")
            logger.info(f"Health check request path: {request.path}")
            logger.info(f"Health check request full path: {request.get_full_path()}")

            # Basic health check - verify database connection
            from django.db import connection
            from datetime import datetime

            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")

            return JsonResponse({
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'database': 'connected',
                'services': {
                    'websocket': 'available',
                    'workflows': 'available',
                    'llm': 'available'
                }
            })
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return JsonResponse({
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class LoggingView(View):
    """
    Error logging endpoint for production mode
    """

    def post(self, request):
        try:
            data = json.loads(request.body)
            level = data.get('level', 'error')
            message = data.get('message', '')
            details = data.get('details', {})

            # Log the frontend error
            if level == 'error':
                logger.error(f"Frontend Error: {message}", extra={'details': details})
            elif level == 'warning':
                logger.warning(f"Frontend Warning: {message}", extra={'details': details})
            else:
                logger.info(f"Frontend Info: {message}", extra={'details': details})

            return JsonResponse({'success': True})

        except json.JSONDecodeError:
            return JsonResponse({'error': 'Invalid JSON'}, status=400)
        except Exception as e:
            logger.error(f"Logging error: {e}")
            return JsonResponse({'error': 'Logging failed'}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class CreateActivityView(View):
    """
    API endpoint to create a new custom activity
    """

    def post(self, request):
        try:
            from apps.activity.models import GenericActivity, ActivityTailored
            from apps.user.models import UserProfile
            from datetime import datetime
            import json

            # Parse request data
            data = json.loads(request.body)

            # Get user profile
            user_profile = None
            if request.user.is_authenticated:
                try:
                    user_profile = UserProfile.objects.get(user=request.user)
                except UserProfile.DoesNotExist:
                    return JsonResponse({'success': False, 'error': 'User profile not found'}, status=400)
            else:
                return JsonResponse({'success': False, 'error': 'Authentication required'}, status=401)

            # Create new generic activity first (as template)
            generic_activity = GenericActivity.objects.create(
                name=data.get('name', ''),
                description=data.get('description', ''),
                code=f"custom_{user_profile.id}_{int(time.time())}",  # Unique code
                duration_range=data.get('duration_range', '15-30 minutes'),
                instructions=data.get('instructions', data.get('description', '')),
                created_on=datetime.now().date(),
                social_requirements={}
            )

            # Get user's current environment
            current_environment = user_profile.current_environment
            if not current_environment:
                # Get first available environment for the user
                current_environment = user_profile.environments.first()
                if not current_environment:
                    return JsonResponse({'success': False, 'error': 'No user environment found'}, status=400)

            # Create tailored activity for the user
            tailored_activity = ActivityTailored.objects.create(
                name=data.get('name', ''),
                description=data.get('description', ''),
                instructions=data.get('instructions', data.get('description', '')),
                created_on=datetime.now().date(),
                user_profile=user_profile,
                generic_activity=generic_activity,
                user_environment=current_environment,
                base_challenge_rating=data.get('base_challenge_rating', 50),
                challengingness={},  # Empty for now
                version=1,
                tailorization_level=100,  # Fully custom
                created_by=user_profile,  # Mark as user-created
                duration_range=data.get('duration_range', '15-30 minutes'),
                social_requirements={}
            )

            return JsonResponse({
                'success': True,
                'activity': {
                    'id': f'tailored-{tailored_activity.id}',
                    'name': tailored_activity.name,
                    'description': tailored_activity.description,
                    'domain': 'custom',
                    'base_challenge_rating': tailored_activity.base_challenge_rating,
                    'type': 'tailored',
                    'icon': '👤'
                }
            })

        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'error': 'Invalid JSON data'}, status=400)
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class TailorActivityView(View):
    """
    API endpoint to create a tailored version of a generic activity
    """

    def post(self, request):
        try:
            from apps.activity.models import GenericActivity, ActivityTailored
            from apps.user.models import UserProfile
            from datetime import datetime
            import json

            # Parse request data
            data = json.loads(request.body)
            generic_activity_id = data.get('generic_activity_id')

            if not generic_activity_id:
                return JsonResponse({'success': False, 'error': 'generic_activity_id required'}, status=400)

            # Get user profile
            user_profile = None
            if request.user.is_authenticated:
                try:
                    user_profile = UserProfile.objects.get(user=request.user)
                except UserProfile.DoesNotExist:
                    return JsonResponse({'success': False, 'error': 'User profile not found'}, status=400)
            else:
                return JsonResponse({'success': False, 'error': 'Authentication required'}, status=401)

            # Get generic activity
            try:
                # Handle both 'generic-123' and '123' formats
                if generic_activity_id.startswith('generic-'):
                    generic_id = generic_activity_id.replace('generic-', '')
                else:
                    generic_id = generic_activity_id

                generic_activity = GenericActivity.objects.get(id=generic_id)
            except GenericActivity.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'Generic activity not found'}, status=404)

            # Get user's current environment
            current_environment = user_profile.current_environment
            if not current_environment:
                current_environment = user_profile.environments.first()
                if not current_environment:
                    return JsonResponse({'success': False, 'error': 'No user environment found'}, status=400)

            # Check if tailored version already exists
            existing_tailored = ActivityTailored.objects.filter(
                user_profile=user_profile,
                generic_activity=generic_activity,
                user_environment=current_environment
            ).first()

            if existing_tailored:
                # Return existing tailored activity
                return JsonResponse({
                    'success': True,
                    'activity': {
                        'id': f'tailored-{existing_tailored.id}',
                        'name': existing_tailored.name,
                        'description': existing_tailored.description,
                        'domain': 'personalized',
                        'base_challenge_rating': existing_tailored.base_challenge_rating,
                        'type': 'tailored',
                        'icon': '⭐'
                    }
                })

            # Create new tailored activity
            tailored_activity = ActivityTailored.objects.create(
                name=generic_activity.name,
                description=generic_activity.description,
                instructions=generic_activity.instructions,
                created_on=datetime.now().date(),
                user_profile=user_profile,
                generic_activity=generic_activity,
                user_environment=current_environment,
                base_challenge_rating=50,  # Default challenge rating for tailored activities
                challengingness={},  # Would be populated by tailoring logic
                version=1,
                tailorization_level=50,  # Moderate tailoring
                created_by=None,  # System-generated
                duration_range=generic_activity.duration_range,
                social_requirements={}
            )

            return JsonResponse({
                'success': True,
                'activity': {
                    'id': f'tailored-{tailored_activity.id}',
                    'name': tailored_activity.name,
                    'description': tailored_activity.description,
                    'domain': 'personalized',
                    'base_challenge_rating': tailored_activity.base_challenge_rating,
                    'type': 'tailored',
                    'icon': '⭐'
                }
            })

        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'error': 'Invalid JSON data'}, status=400)
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class CustomActivityView(View):
    """
    API endpoint to create custom activities as HistoryEvent records
    """

    def post(self, request):
        try:
            from apps.main.models import HistoryEvent, Wheel, WheelItem
            from apps.user.models import UserProfile
            from django.contrib.contenttypes.models import ContentType
            import json
            import time

            # Parse request data
            data = json.loads(request.body)

            # Get user profile
            user_profile = None
            if request.user.is_authenticated:
                try:
                    user_profile = UserProfile.objects.get(user=request.user)
                except UserProfile.DoesNotExist:
                    return JsonResponse({'success': False, 'error': 'User profile not found'}, status=400)
            else:
                return JsonResponse({'success': False, 'error': 'Authentication required'}, status=401)

            # Validate required fields
            title = data.get('title', '').strip()
            description = data.get('description', '').strip()

            if not title or not description:
                return JsonResponse({'success': False, 'error': 'Title and description are required'}, status=400)

            # Get user's current wheel
            wheels = Wheel.objects.filter(name__icontains=user_profile.profile_name).order_by('-created_at')
            if not wheels.exists():
                return JsonResponse({'success': False, 'error': 'No wheel found for user'}, status=404)

            current_wheel = wheels.first()

            # Create custom activity metadata
            activity_metadata = {
                'title': title,
                'description': description,
                'duration_range': data.get('duration_range', '15-30 minutes'),
                'challengingness': data.get('challengingness', 50),
                'comfort_level': data.get('comfort_level', 50),
                'created_timestamp': time.time(),
                'activity_type': 'custom_user_created',
                'domain': 'custom',
                'base_challenge_rating': data.get('challengingness', 50)
            }

            # Create HistoryEvent for custom activity creation using schema validation
            from apps.main.services.history_event_service import HistoryEventService

            history_event = HistoryEventService.create_custom_activity_event(
                wheel=current_wheel,
                user_profile=user_profile,
                title=title,
                description=description,
                duration_range=data.get('duration_range', '15-30 minutes'),
                challengingness=data.get('challengingness', 50),
                comfort_level=data.get('comfort_level', 50),
                domain='custom'
            )

            # Create a virtual wheel item for the custom activity
            # We'll use the HistoryEvent ID as a reference
            virtual_wheel_item_data = {
                'id': f'custom-{history_event.id}',
                'name': title,
                'description': description,
                'percentage': 0,  # Will be calculated when added to wheel
                'color': '#9B59B6',  # Purple for custom activities
                'domain': 'custom',
                'base_challenge_rating': activity_metadata['challengingness'],
                'activity_tailored_id': f'custom-{history_event.id}',
                'type': 'custom',
                'history_event_id': history_event.id
            }

            return JsonResponse({
                'success': True,
                'custom_activity': virtual_wheel_item_data,
                'history_event_id': history_event.id,
                'message': 'Custom activity created successfully'
            })

        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'error': 'Invalid JSON data'}, status=400)
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class EventTrackingView(View):
    """
    API endpoint to track user events as HistoryEvent records
    """

    def post(self, request):
        try:
            from apps.main.models import HistoryEvent
            from apps.user.models import UserProfile
            from django.contrib.contenttypes.models import ContentType
            from django.contrib.auth.models import User
            import json

            # Parse request data
            data = json.loads(request.body)

            # Get user profile - handle debug mode, staff impersonation, and authentication
            user_profile = None
            target_user = None
            authenticated_user = request.user if request.user.is_authenticated else None

            # Check if we're in debug mode (development environment)
            is_debug_mode = getattr(settings, 'DEBUG', False)

            # Check for debug user selection (staff impersonation)
            debug_user_id = request.headers.get('X-Debug-User-ID')
            if not debug_user_id:
                # Fallback: check for common debug headers or session data
                debug_user_id = request.session.get('debug_user_id')

            # DEBUG: Log authentication state for troubleshooting
            logger.debug(f"🔐 Enhanced Authentication Debug (EventTrackingView):")
            logger.debug(f"   Debug mode: {is_debug_mode}")
            logger.debug(f"   Authenticated user: {authenticated_user}")
            logger.debug(f"   Authenticated user ID: {getattr(authenticated_user, 'id', 'None')}")
            logger.debug(f"   Authenticated user username: {getattr(authenticated_user, 'username', 'None')}")
            logger.debug(f"   Is staff: {getattr(authenticated_user, 'is_staff', False)}")
            logger.debug(f"   Is superuser: {getattr(authenticated_user, 'is_superuser', False)}")
            logger.debug(f"   Debug user ID header: {debug_user_id}")

            # SCENARIO 1: Debug mode with staff impersonation
            if is_debug_mode and authenticated_user and (authenticated_user.is_staff or authenticated_user.is_superuser) and debug_user_id:
                logger.debug(f"🔐 SCENARIO 1: Staff impersonation in debug mode")
                try:
                    # Staff user is impersonating another user for debugging
                    target_user_profile = UserProfile.objects.get(id=debug_user_id)
                    target_user = target_user_profile.user
                    user_profile = target_user_profile
                    logger.debug(f"✅ Staff user {authenticated_user.username} impersonating user {target_user.username} (Profile: {user_profile.profile_name})")
                except UserProfile.DoesNotExist:
                    logger.error(f"❌ Debug user profile {debug_user_id} not found for staff impersonation")
                    return JsonResponse({'success': False, 'error': f'Debug user profile {debug_user_id} not found'}, status=400)

            # SCENARIO 2: Debug mode without authentication (fallback to test users)
            elif is_debug_mode and not authenticated_user:
                logger.debug(f"🔐 SCENARIO 2: Debug mode fallback authentication")
                # In debug mode without authentication, use a default test user
                for username in ['phiphi', 'test_adhd_student_7b806ebc']:
                    try:
                        target_user = User.objects.get(username=username)
                        logger.debug(f"✅ Found fallback user: {username}")
                        break
                    except User.DoesNotExist:
                        logger.debug(f"❌ Fallback user not found: {username}")
                        continue

                if not target_user:
                    # Final fallback to first available user with profile
                    user_profiles = UserProfile.objects.select_related('user').filter(user__isnull=False)
                    if user_profiles.exists():
                        user_profile = user_profiles.first()
                        target_user = user_profile.user
                        logger.debug(f"✅ Using final fallback user: {target_user.username}")
                    else:
                        logger.error(f"❌ No fallback users available")
                        return JsonResponse({'success': False, 'error': 'No user available'}, status=401)

            # SCENARIO 3: Production mode or authenticated user with their own profile
            elif authenticated_user:
                logger.debug(f"🔐 SCENARIO 3: Production/authenticated mode")
                auth_header = request.headers.get('Authorization', '')
                if auth_header.startswith('Bearer '):
                    # TODO: Implement proper token validation in production
                    logger.debug(f"❌ Bearer token authentication not implemented")
                    return JsonResponse({'success': False, 'error': 'Token authentication not implemented'}, status=401)
                else:
                    target_user = authenticated_user
                    logger.debug(f"✅ Using authenticated user: {target_user.username} (ID: {target_user.id})")

            # SCENARIO 4: No authentication - check if anonymous tracking is allowed
            else:
                logger.debug(f"🔐 SCENARIO 4: No authentication")

                # Allow anonymous tracking for specific non-sensitive event types
                event_type = data.get('event_type', '')
                anonymous_allowed_events = [
                    'page_view',
                    'app_launched',
                    'connection_established',
                    'frontend_error_temporary',
                    'frontend_error_warning'
                ]

                if event_type in anonymous_allowed_events:
                    logger.debug(f"✅ Allowing anonymous tracking for event: {event_type}")
                    # Create anonymous user profile or use a default one
                    try:
                        from apps.user.models import UserProfile
                        anonymous_profile = UserProfile.objects.filter(profile_name='Anonymous').first()
                        if not anonymous_profile:
                            logger.debug(f"❌ No anonymous profile found, rejecting anonymous tracking")
                            return JsonResponse({'success': False, 'error': 'Authentication required'}, status=401)
                        user_profile = anonymous_profile
                        target_user = anonymous_profile.user
                        logger.debug(f"✅ Using anonymous profile for tracking: {user_profile.profile_name}")
                    except Exception as e:
                        logger.error(f"❌ Failed to get anonymous profile: {e}")
                        return JsonResponse({'success': False, 'error': 'Authentication required'}, status=401)
                else:
                    logger.debug(f"❌ Event type '{event_type}' requires authentication")
                    return JsonResponse({'success': False, 'error': 'Authentication required'}, status=401)

            # Get user profile if not already set
            if not user_profile and target_user:
                try:
                    user_profile = UserProfile.objects.get(user=target_user)
                    logger.debug(f"✅ Found user profile: {user_profile.profile_name} (ID: {user_profile.id})")
                except UserProfile.DoesNotExist:
                    logger.error(f"❌ User profile not found for user: {target_user.username} (ID: {target_user.id})")
                    return JsonResponse({'success': False, 'error': 'User profile not found'}, status=400)

            # Security validation: Ensure we have a valid user profile
            if not user_profile:
                logger.error(f"❌ No user profile resolved through any authentication scenario")
                return JsonResponse({'success': False, 'error': 'User profile resolution failed'}, status=400)

            # Extract event data
            event_type = data.get('event_type')
            content_type_name = data.get('content_type')
            object_id = data.get('object_id')
            secondary_content_type_name = data.get('secondary_content_type')
            secondary_object_id = data.get('secondary_object_id')
            details = data.get('details', {})

            if not event_type or not content_type_name or not object_id:
                return JsonResponse({'success': False, 'error': 'event_type, content_type, and object_id are required'}, status=400)

            # Get content type
            try:
                if content_type_name == 'UserProfile':
                    content_type = ContentType.objects.get_for_model(UserProfile)
                elif content_type_name == 'Wheel':
                    from apps.main.models import Wheel
                    content_type = ContentType.objects.get_for_model(Wheel)
                elif content_type_name == 'WheelItem':
                    from apps.main.models import WheelItem
                    content_type = ContentType.objects.get_for_model(WheelItem)
                elif content_type_name == 'ActivityTailored':
                    from apps.activity.models import ActivityTailored
                    content_type = ContentType.objects.get_for_model(ActivityTailored)
                elif content_type_name in ['search', 'test', 'generic']:
                    # For generic events like search, testing, etc., use UserProfile as content type
                    content_type = ContentType.objects.get_for_model(UserProfile)
                else:
                    return JsonResponse({'success': False, 'error': f'Unsupported content type: {content_type_name}'}, status=400)
            except Exception as e:
                return JsonResponse({'success': False, 'error': f'Invalid content type: {str(e)}'}, status=400)

            # Get secondary content type if provided
            secondary_content_type = None
            if secondary_content_type_name:
                try:
                    if secondary_content_type_name == 'UserProfile':
                        secondary_content_type = ContentType.objects.get_for_model(UserProfile)
                    elif secondary_content_type_name == 'Wheel':
                        from apps.main.models import Wheel
                        secondary_content_type = ContentType.objects.get_for_model(Wheel)
                    elif secondary_content_type_name == 'WheelItem':
                        from apps.main.models import WheelItem
                        secondary_content_type = ContentType.objects.get_for_model(WheelItem)
                    elif secondary_content_type_name == 'ActivityTailored':
                        from apps.activity.models import ActivityTailored
                        secondary_content_type = ContentType.objects.get_for_model(ActivityTailored)
                except Exception as e:
                    return JsonResponse({'success': False, 'error': f'Invalid secondary content type: {str(e)}'}, status=400)

            # Create HistoryEvent with schema validation
            try:
                from apps.main.services.history_event_service import HistoryEventService
                from apps.main.schemas.history_event_schemas import validate_event_details

                # Validate details against schema if available
                validated_details = validate_event_details(event_type, details)

                # Create the event using the raw method for flexibility with content types
                history_event = HistoryEvent.objects.create(
                    event_type=event_type,
                    content_type=content_type,
                    object_id=str(object_id),
                    secondary_content_type=secondary_content_type,
                    secondary_object_id=str(secondary_object_id) if secondary_object_id else None,
                    user_profile=user_profile,
                    details=validated_details
                )
            except ValueError as validation_error:
                # If validation fails, log the error and use original details
                logger.warning(f"Schema validation failed for {event_type}: {validation_error}")
                history_event = HistoryEvent.objects.create(
                    event_type=event_type,
                    content_type=content_type,
                    object_id=str(object_id),
                    secondary_content_type=secondary_content_type,
                    secondary_object_id=str(secondary_object_id) if secondary_object_id else None,
                    user_profile=user_profile,
                    details=details
                )

            return JsonResponse({
                'success': True,
                'event_id': history_event.id,
                'message': 'Event tracked successfully'
            })

        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'error': 'Invalid JSON data'}, status=400)
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class HistoryEventSchemaView(View):
    """
    API endpoint to get HistoryEvent schema information
    """

    def get(self, request):
        """Get schema information for HistoryEvent details"""
        try:
            from apps.main.services.history_event_service import HistoryEventService

            event_type = request.GET.get('event_type')

            if event_type:
                # Get schema for specific event type
                schema_info = HistoryEventService.get_schema_info(event_type)
                return JsonResponse({
                    'success': True,
                    'schema': schema_info
                })
            else:
                # List all available schemas
                schemas_info = HistoryEventService.list_available_schemas()
                return JsonResponse({
                    'success': True,
                    **schemas_info
                })

        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class UserFeedbackView(View):
    """
    API endpoint to create user feedback for various content types
    """

    def post(self, request):
        try:
            from apps.main.models import UserFeedback
            from apps.user.models import UserProfile
            from django.contrib.contenttypes.models import ContentType
            from django.contrib.auth.models import User
            import json

            # Parse request data
            data = json.loads(request.body)

            # Get user profile - handle debug mode and authentication
            user_profile = None
            user = None

            # Check if we're in debug mode (development environment)
            is_debug_mode = getattr(settings, 'DEBUG', False)

            if is_debug_mode:
                # In debug mode, use a default test user (PhiPhi - ID: 3)
                try:
                    user = User.objects.get(id=3)  # PhiPhi user
                except User.DoesNotExist:
                    # Fallback to first available user with profile
                    user_profiles = UserProfile.objects.select_related('user').all()
                    if user_profiles:
                        user = user_profiles.first().user
                    else:
                        return JsonResponse({'success': False, 'error': 'No user available for feedback'}, status=401)
            else:
                # Production mode - require proper authentication
                auth_header = request.headers.get('Authorization', '')
                if auth_header.startswith('Bearer '):
                    # TODO: Implement proper token validation in production
                    return JsonResponse({'success': False, 'error': 'Token authentication not implemented'}, status=401)
                elif request.user.is_authenticated:
                    user = request.user
                else:
                    return JsonResponse({'success': False, 'error': 'Authentication required'}, status=401)

            # Get user profile
            try:
                user_profile = UserProfile.objects.get(user=user)
            except UserProfile.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'User profile not found'}, status=400)

            # Validate required fields
            feedback_type = data.get('feedback_type')
            content_type_name = data.get('content_type')
            object_id = data.get('object_id')
            user_comment = data.get('user_comment', '')

            if not all([feedback_type, content_type_name, object_id]):
                return JsonResponse({
                    'success': False,
                    'error': 'feedback_type, content_type, and object_id are required'
                }, status=400)

            # Get content type
            try:
                content_type = ContentType.objects.get(model=content_type_name.lower())
            except ContentType.DoesNotExist:
                return JsonResponse({
                    'success': False,
                    'error': f'Invalid content_type: {content_type_name}'
                }, status=400)

            # Create feedback
            feedback = UserFeedback.objects.create(
                feedback_type=feedback_type,
                content_type=content_type,
                object_id=str(object_id),
                user_profile=user_profile,
                user_comment=user_comment,
                criticality=data.get('criticality', 1),
                context_data=data.get('context_data', {}),
                slack_payload={}
            )

            return JsonResponse({
                'success': True,
                'feedback_id': feedback.id,
                'message': 'Feedback submitted successfully'
            })

        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'error': 'Invalid JSON data'}, status=400)
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class WheelItemManagementView(View):
    """
    API endpoint to manage wheel items (add/remove)
    """

    def delete(self, request, wheel_item_id):
        """Remove a wheel item and trigger wheel regeneration"""
        try:
            from apps.main.models import WheelItem, Wheel
            from apps.user.models import UserProfile
            from django.contrib.auth.models import User
            import json

            # Get user profile - handle debug mode, staff impersonation, and authentication
            user_profile = None
            target_user = None
            authenticated_user = request.user if request.user.is_authenticated else None

            # Check if we're in debug mode (development environment)
            is_debug_mode = getattr(settings, 'DEBUG', False)

            # Check for debug user selection (staff impersonation)
            debug_user_id = request.headers.get('X-Debug-User-ID')
            if not debug_user_id:
                # Fallback: check for common debug headers or session data
                debug_user_id = request.session.get('debug_user_id')

            # DEBUG: Log authentication state for troubleshooting
            logger.debug(f"🔐 Enhanced Authentication Debug:")
            logger.debug(f"   Debug mode: {is_debug_mode}")
            logger.debug(f"   Authenticated user: {authenticated_user}")
            logger.debug(f"   Authenticated user ID: {getattr(authenticated_user, 'id', 'None')}")
            logger.debug(f"   Authenticated user username: {getattr(authenticated_user, 'username', 'None')}")
            logger.debug(f"   Is staff: {getattr(authenticated_user, 'is_staff', False)}")
            logger.debug(f"   Is superuser: {getattr(authenticated_user, 'is_superuser', False)}")
            logger.debug(f"   Debug user ID header: {debug_user_id}")

            # SCENARIO 1: Debug mode with staff impersonation
            if is_debug_mode and authenticated_user and (authenticated_user.is_staff or authenticated_user.is_superuser) and debug_user_id:
                logger.debug(f"🔐 SCENARIO 1: Staff impersonation in debug mode")
                try:
                    # Staff user is impersonating another user for debugging
                    target_user_profile = UserProfile.objects.get(id=debug_user_id)
                    target_user = target_user_profile.user
                    user_profile = target_user_profile
                    logger.debug(f"✅ Staff user {authenticated_user.username} impersonating user {target_user.username} (Profile: {user_profile.profile_name})")
                except UserProfile.DoesNotExist:
                    logger.error(f"❌ Debug user profile {debug_user_id} not found for staff impersonation")
                    return JsonResponse({'success': False, 'error': f'Debug user profile {debug_user_id} not found'}, status=400)

            # SCENARIO 2: Debug mode without authentication (fallback to test users)
            elif is_debug_mode and not authenticated_user:
                logger.debug(f"🔐 SCENARIO 2: Debug mode fallback authentication")
                # In debug mode without authentication, use a default test user
                for username in ['phiphi', 'test_adhd_student_7b806ebc']:
                    try:
                        target_user = User.objects.get(username=username)
                        logger.debug(f"✅ Found fallback user: {username}")
                        break
                    except User.DoesNotExist:
                        logger.debug(f"❌ Fallback user not found: {username}")
                        continue

                if not target_user:
                    # Final fallback to first available user with profile
                    user_profiles = UserProfile.objects.select_related('user').filter(user__isnull=False)
                    if user_profiles.exists():
                        user_profile = user_profiles.first()
                        target_user = user_profile.user
                        logger.debug(f"✅ Using final fallback user: {target_user.username}")
                    else:
                        logger.error(f"❌ No fallback users available")
                        return JsonResponse({'success': False, 'error': 'No user available'}, status=401)

            # SCENARIO 3: Production mode or authenticated user with their own profile
            elif authenticated_user:
                logger.debug(f"🔐 SCENARIO 3: Production/authenticated mode")
                auth_header = request.headers.get('Authorization', '')
                if auth_header.startswith('Bearer '):
                    # TODO: Implement proper token validation in production
                    logger.debug(f"❌ Bearer token authentication not implemented")
                    return JsonResponse({'success': False, 'error': 'Token authentication not implemented'}, status=401)
                else:
                    target_user = authenticated_user
                    logger.debug(f"✅ Using authenticated user: {target_user.username} (ID: {target_user.id})")

            # SCENARIO 4: No authentication
            else:
                logger.debug(f"🔐 SCENARIO 4: No authentication")
                return JsonResponse({'success': False, 'error': 'Authentication required'}, status=401)

            # Get user profile if not already set
            if not user_profile and target_user:
                try:
                    user_profile = UserProfile.objects.get(user=target_user)
                    logger.debug(f"✅ Found user profile: {user_profile.profile_name} (ID: {user_profile.id})")
                except UserProfile.DoesNotExist:
                    logger.error(f"❌ User profile not found for user: {target_user.username} (ID: {target_user.id})")
                    return JsonResponse({'success': False, 'error': 'User profile not found'}, status=400)

            # Security validation: Ensure we have a valid user profile
            if not user_profile:
                logger.error(f"❌ No user profile resolved through any authentication scenario")
                return JsonResponse({'success': False, 'error': 'User profile resolution failed'}, status=400)

            # ROBUST SOLUTION: Use centralized wheel service for consistent wheel identification
            try:
                from apps.main.services.wheel_service import WheelService

                # Use centralized service to remove activity from current wheel
                result = WheelService.remove_activity_from_current_wheel(user_profile, wheel_item_id)

                if result['success']:
                    return JsonResponse(result)
                else:
                    return JsonResponse(result, status=400)

            except Exception as e:
                logger.error(f"❌ Error in wheel item removal: {e}")
                return JsonResponse({'success': False, 'error': str(e)}, status=500)
        except Exception as e:
                logger.error(f"❌ Error in wheel item removal: {e}")
                return JsonResponse({'success': False, 'error': str(e)}, status=500)




    def post(self, request):
        """Add an activity to the current wheel"""
        try:
            from apps.main.models import Wheel
            from apps.activity.models import ActivityTailored, GenericActivity
            from apps.user.models import UserProfile
            from django.contrib.auth.models import User
            import json

            # Parse request data
            data = json.loads(request.body)
            activity_id = data.get('activity_id')
            activity_type = data.get('activity_type', 'tailored')  # 'tailored', 'generic', or 'custom'

            if not activity_id:
                return JsonResponse({'success': False, 'error': 'activity_id is required'}, status=400)

            # Ensure activity_id is a string for consistent handling
            activity_id = str(activity_id)

            # Get user profile - handle debug mode, staff impersonation, and authentication
            user_profile = None
            target_user = None
            authenticated_user = request.user if request.user.is_authenticated else None

            # Check if we're in debug mode (development environment)
            is_debug_mode = getattr(settings, 'DEBUG', False)

            # Check for debug user selection (staff impersonation)
            debug_user_id = request.headers.get('X-Debug-User-ID')
            if not debug_user_id:
                # Fallback: check for common debug headers or session data
                debug_user_id = request.session.get('debug_user_id')

            # DEBUG: Log authentication state for troubleshooting
            logger.debug(f"🔐 Enhanced Authentication Debug (WheelItemManagementView POST):")
            logger.debug(f"   Debug mode: {is_debug_mode}")
            logger.debug(f"   Authenticated user: {authenticated_user}")
            logger.debug(f"   Authenticated user ID: {getattr(authenticated_user, 'id', 'None')}")
            logger.debug(f"   Authenticated user username: {getattr(authenticated_user, 'username', 'None')}")
            logger.debug(f"   Is staff: {getattr(authenticated_user, 'is_staff', False)}")
            logger.debug(f"   Is superuser: {getattr(authenticated_user, 'is_superuser', False)}")
            logger.debug(f"   Debug user ID header: {debug_user_id}")

            # SCENARIO 1: Debug mode with staff impersonation
            if is_debug_mode and authenticated_user and (authenticated_user.is_staff or authenticated_user.is_superuser) and debug_user_id:
                logger.debug(f"🔐 SCENARIO 1: Staff impersonation in debug mode")
                try:
                    # Staff user is impersonating another user for debugging
                    target_user_profile = UserProfile.objects.get(id=debug_user_id)
                    target_user = target_user_profile.user
                    user_profile = target_user_profile
                    logger.debug(f"✅ Staff user {authenticated_user.username} impersonating user {target_user.username} (Profile: {user_profile.profile_name})")
                except UserProfile.DoesNotExist:
                    logger.error(f"❌ Debug user profile {debug_user_id} not found for staff impersonation")
                    return JsonResponse({'success': False, 'error': f'Debug user profile {debug_user_id} not found'}, status=400)

            # SCENARIO 2: Debug mode without authentication (fallback to test users)
            elif is_debug_mode and not authenticated_user:
                logger.debug(f"🔐 SCENARIO 2: Debug mode fallback authentication")
                # In debug mode without authentication, use a default test user
                for username in ['phiphi', 'test_adhd_student_7b806ebc']:
                    try:
                        target_user = User.objects.get(username=username)
                        logger.debug(f"✅ Found fallback user: {username}")
                        break
                    except User.DoesNotExist:
                        logger.debug(f"❌ Fallback user not found: {username}")
                        continue

                if not target_user:
                    # Final fallback to first available user with profile
                    user_profiles = UserProfile.objects.select_related('user').filter(user__isnull=False)
                    if user_profiles.exists():
                        user_profile = user_profiles.first()
                        target_user = user_profile.user
                        logger.debug(f"✅ Using final fallback user: {target_user.username}")
                    else:
                        logger.error(f"❌ No fallback users available")
                        return JsonResponse({'success': False, 'error': 'No user available'}, status=401)

            # SCENARIO 3: Production mode or authenticated user with their own profile
            elif authenticated_user:
                logger.debug(f"🔐 SCENARIO 3: Production/authenticated mode")
                auth_header = request.headers.get('Authorization', '')
                if auth_header.startswith('Bearer '):
                    # TODO: Implement proper token validation in production
                    logger.debug(f"❌ Bearer token authentication not implemented")
                    return JsonResponse({'success': False, 'error': 'Token authentication not implemented'}, status=401)
                else:
                    target_user = authenticated_user
                    logger.debug(f"✅ Using authenticated user: {target_user.username} (ID: {target_user.id})")

            # SCENARIO 4: No authentication
            else:
                logger.debug(f"🔐 SCENARIO 4: No authentication")
                return JsonResponse({'success': False, 'error': 'Authentication required'}, status=401)

            # Get user profile if not already set
            if not user_profile and target_user:
                try:
                    user_profile = UserProfile.objects.get(user=target_user)
                    logger.debug(f"✅ Found user profile: {user_profile.profile_name} (ID: {user_profile.id})")
                except UserProfile.DoesNotExist:
                    logger.error(f"❌ User profile not found for user: {target_user.username} (ID: {target_user.id})")
                    return JsonResponse({'success': False, 'error': 'User profile not found'}, status=400)

            # Security validation: Ensure we have a valid user profile
            if not user_profile:
                logger.error(f"❌ No user profile resolved through any authentication scenario")
                return JsonResponse({'success': False, 'error': 'User profile resolution failed'}, status=400)

            # Use centralized wheel service for consistent wheel management
            from apps.main.services.wheel_service import WheelService

            # Get activity based on type
            activity_tailored = None
            if activity_type == 'custom':
                # Handle custom activity (from HistoryEvent)
                try:
                    from apps.main.models import HistoryEvent
                    from django.contrib.contenttypes.models import ContentType

                    # Extract history event ID from custom activity ID
                    if activity_id.startswith('custom-'):
                        history_event_id = activity_id.replace('custom-', '')
                    else:
                        history_event_id = activity_id

                    # Get the HistoryEvent with custom activity data
                    history_event = HistoryEvent.objects.get(
                        id=history_event_id,
                        event_type='custom_activity_created',
                        user_profile=user_profile
                    )

                    # Create a temporary ActivityTailored object for the custom activity
                    # This allows us to use the existing wheel item structure
                    current_environment = user_profile.current_environment
                    if not current_environment:
                        current_environment = user_profile.environments.first()
                        if not current_environment:
                            return JsonResponse({'success': False, 'error': 'No user environment found'}, status=400)

                    activity_metadata = history_event.details
                    # For custom activities, we need to create a minimal generic activity first
                    # or handle the case where generic_activity can be null
                    from apps.activity.models import GenericActivity

                    # Create a temporary generic activity for custom activities
                    # This is a workaround since the model requires a generic_activity
                    temp_generic_activity = GenericActivity.objects.create(
                        name=f"Custom: {activity_metadata['title']}",
                        description=activity_metadata['description'],
                        instructions=activity_metadata['description'],
                        created_on=timezone.now().date(),
                        duration_range=activity_metadata.get('duration_range', '15-30 minutes'),
                        social_requirements={}
                    )

                    activity_tailored = ActivityTailored.objects.create(
                        name=activity_metadata['title'],
                        description=activity_metadata['description'],
                        instructions=activity_metadata['description'],  # Use description as instructions
                        created_on=timezone.now().date(),
                        user_profile=user_profile,
                        generic_activity=temp_generic_activity,  # Use the temporary generic activity
                        user_environment=current_environment,
                        base_challenge_rating=activity_metadata.get('challengingness', 50),
                        challengingness={'custom': activity_metadata.get('challengingness', 50)},
                        version=1,
                        tailorization_level=100,  # Fully custom
                        created_by=user_profile,
                        duration_range=activity_metadata.get('duration_range', '15-30 minutes'),
                        social_requirements={}
                    )

                except HistoryEvent.DoesNotExist:
                    return JsonResponse({'success': False, 'error': 'Custom activity not found'}, status=404)
            elif activity_type == 'tailored':
                # Handle tailored activity
                try:
                    # Remove 'tailored-' prefix if present
                    if activity_id.startswith('tailored-'):
                        activity_id = activity_id.replace('tailored-', '')
                    activity_tailored = ActivityTailored.objects.get(id=activity_id)
                except ActivityTailored.DoesNotExist:
                    return JsonResponse({'success': False, 'error': 'Tailored activity not found'}, status=404)
            else:
                # Handle generic activity - need to create tailored version
                try:
                    # Remove 'generic-' prefix if present
                    if activity_id.startswith('generic-'):
                        activity_id = activity_id.replace('generic-', '')
                    generic_activity = GenericActivity.objects.get(id=activity_id)

                    # Get user's current environment
                    current_environment = user_profile.current_environment
                    if not current_environment:
                        current_environment = user_profile.environments.first()
                        if not current_environment:
                            return JsonResponse({'success': False, 'error': 'No user environment found'}, status=400)

                    # Try to get existing tailored activity first
                    try:
                        activity_tailored = ActivityTailored.objects.filter(
                            user_profile=user_profile,
                            generic_activity=generic_activity,
                            user_environment=current_environment
                        ).first()

                        if not activity_tailored:
                            # Create new tailored activity if none exists
                            activity_tailored = ActivityTailored.objects.create(
                                user_profile=user_profile,
                                generic_activity=generic_activity,
                                user_environment=current_environment,
                                name=generic_activity.name,
                                description=generic_activity.description,
                                instructions=generic_activity.instructions,
                                created_on=timezone.now().date(),
                                base_challenge_rating=50,  # Default challenge rating
                                challengingness={},
                                version=1,
                                tailorization_level=50,
                                created_by=None,
                                duration_range=generic_activity.duration_range,
                                social_requirements={}
                            )
                            logger.info(f"✅ Created new ActivityTailored {activity_tailored.id} for {generic_activity.name}")
                        else:
                            logger.info(f"✅ Using existing ActivityTailored {activity_tailored.id} for {generic_activity.name}")

                    except Exception as e:
                        logger.error(f"❌ Error creating/getting ActivityTailored: {e}")
                        return JsonResponse({'success': False, 'error': f'Error processing activity: {str(e)}'}, status=500)
                except GenericActivity.DoesNotExist:
                    return JsonResponse({'success': False, 'error': 'Generic activity not found'}, status=404)

            # Use centralized wheel service to add activity
            result = WheelService.add_activity_to_current_wheel(user_profile, activity_tailored)

            if result['success']:
                return JsonResponse(result)
            else:
                return JsonResponse(result, status=400)

        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'error': 'Invalid JSON data'}, status=400)
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=500)

    def _get_activity_color(self, activity_tailored, index=None):
        """Get color for activity based on domain using the centralized color system"""
        try:
            # Import the centralized color function
            from apps.main.agents.tools.activity_tools import _get_activity_color

            # Try to get domain from the activity_tailored object
            domain = 'general'  # Default fallback

            # Check if activity_tailored has a domain attribute
            if hasattr(activity_tailored, 'domain') and activity_tailored.domain:
                domain = activity_tailored.domain
            # Check if it has a generic_activity relationship with domain
            elif hasattr(activity_tailored, 'generic_activity') and activity_tailored.generic_activity and hasattr(activity_tailored.generic_activity, 'domain'):
                domain = activity_tailored.generic_activity.domain
            # Check if it has a related activity with domain
            elif hasattr(activity_tailored, 'activity') and activity_tailored.activity and hasattr(activity_tailored.activity, 'domain'):
                domain = activity_tailored.activity.domain

            # Use the centralized color function with index
            return _get_activity_color(domain, index)

        except Exception as e:
            # Fallback to domain colors from settings if the import fails
            domain_colors = getattr(settings, 'ACTIVITY_DOMAIN_COLORS', {})
            return domain_colors.get('general', '#95A5A6')


@method_decorator(csrf_exempt, name='dispatch')
class UserProfileDetailView(View):
    """
    API endpoint to get comprehensive user profile data
    """

    def get(self, request, user_id=None):
        try:
            from apps.user.models import UserProfile, Demographics, Preference, UserEnvironment

            # SECURITY FIX: Ensure user authentication
            if not request.user.is_authenticated:
                return JsonResponse({'success': False, 'error': 'Authentication required'}, status=401)

            # SECURITY FIX: Authorization check - users can only access their own profile
            # unless they are staff (for debug purposes)
            if user_id:
                if not request.user.is_staff:
                    return JsonResponse({'success': False, 'error': 'Access denied'}, status=403)
                try:
                    user_profile = UserProfile.objects.select_related('user', 'current_environment').get(id=user_id)
                except UserProfile.DoesNotExist:
                    return JsonResponse({'success': False, 'error': 'User profile not found'}, status=404)
            else:
                # Get authenticated user's own profile
                try:
                    user_profile = UserProfile.objects.select_related('user', 'current_environment').get(user=request.user)
                except UserProfile.DoesNotExist:
                    return JsonResponse({'success': False, 'error': 'User profile not found'}, status=404)

            # Get demographics
            demographics = None
            try:
                demo = Demographics.objects.get(user_profile=user_profile)
                demographics = {
                    'full_name': demo.full_name,
                    'age': demo.age,
                    'gender': demo.gender,
                    'location': demo.location,
                    'language': demo.language,
                    'occupation': demo.occupation,
                }
            except Demographics.DoesNotExist:
                pass

            # Get current environment details
            environment = None
            if user_profile.current_environment:
                env = user_profile.current_environment
                environment = {
                    'id': env.id,
                    'name': env.environment_name,
                    'description': env.environment_description,
                    'living_situation': env.environment_details.get('living_situation', 'Not specified'),
                    'available_resources': env.environment_details.get('available_resources', 'Not specified'),
                    'constraints': env.environment_details.get('constraints', 'Not specified'),
                    'is_current': env.is_current
                }

            # Get user environments
            environments = []
            for env in UserEnvironment.objects.filter(user_profile=user_profile):
                environments.append({
                    'id': env.id,
                    'name': env.environment_name,
                    'description': env.environment_description,
                    'is_current': env.is_current,
                    'details': env.environment_details
                })

            # Get preferences
            preferences = []
            for pref in Preference.objects.filter(user_profile=user_profile):
                preferences.append({
                    'name': pref.pref_name,
                    'description': pref.pref_description,
                    'strength': pref.pref_strength,
                })

            # Build comprehensive profile data
            profile_data = {
                'id': str(user_profile.id),
                'name': user_profile.profile_name,
                'description': f"User profile for {user_profile.profile_name}",
                'is_real': user_profile.is_real,
                'demographics': demographics,
                'environment': environment,
                'environments': environments,
                'preferences': preferences,
                'goals': {
                    'short_term': 'Not set',  # These would come from a Goals model if implemented
                    'long_term': 'Not set',
                    'motivation': 'Not set'
                },
                'created_at': getattr(user_profile, 'created_at', None)
            }

            return JsonResponse({
                'success': True,
                'profile': profile_data
            })

        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class BetaSignupView(View):
    """
    API endpoint to handle beta signup requests from interested users
    """

    def post(self, request):
        try:
            from apps.main.models import BetaSignup
            import json
            import re

            # Parse request data
            data = json.loads(request.body)
            email = data.get('email', '').strip().lower()
            message = data.get('message', '').strip()

            # Validate email
            if not email:
                return JsonResponse({
                    'success': False,
                    'error': 'Email address is required'
                }, status=400)

            # Basic email validation
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, email):
                return JsonResponse({
                    'success': False,
                    'error': 'Please enter a valid email address'
                }, status=400)

            # Check if email already exists
            if BetaSignup.objects.filter(email=email).exists():
                return JsonResponse({
                    'success': False,
                    'error': 'This email address has already been registered for beta access'
                }, status=400)

            # Get client metadata
            ip_address = self._get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', '')

            # Create beta signup record
            beta_signup = BetaSignup.objects.create(
                email=email,
                message=message,
                ip_address=ip_address,
                user_agent=user_agent
            )

            return JsonResponse({
                'success': True,
                'message': 'Thank you for your interest! We\'ll contact you when beta access becomes available.',
                'signup_id': str(beta_signup.id)
            })

        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'error': 'Invalid request data'
            }, status=400)
        except Exception as e:
            logger.error(f"Beta signup error: {str(e)}")
            return JsonResponse({
                'success': False,
                'error': 'An error occurred while processing your request. Please try again.'
            }, status=500)

    def _get_client_ip(self, request):
        """Get the client's IP address from the request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
