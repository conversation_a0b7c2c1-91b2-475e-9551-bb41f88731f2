#!/bin/bash

# Setup script for Logs Plugin Dashboard for Chronological Benchmark Analysis
# This script sets up the database view and validates the Grafana configuration

set -e

echo "🚀 Setting up Logs Plugin Dashboard for Chronological Benchmark Analysis..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the correct directory
if [ ! -f "docker-compose.yml" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

print_status "Checking Docker services..."

# Check if services are running
if ! docker-compose ps | grep -q "Up"; then
    print_warning "Docker services are not running. Starting them..."
    docker-compose up -d
    sleep 10
fi

print_status "Running database migrations..."

# Run the migration to create the chronological events view
docker-compose run --rm web python manage.py migrate

print_success "Database migration completed"

print_status "Validating chronological events view..."

# Test the view exists and has data
docker-compose exec db psql -U postgres -d mydb -c "
SELECT 
    COUNT(*) as total_events,
    COUNT(DISTINCT run_id) as unique_runs,
    COUNT(DISTINCT event_type) as event_types
FROM grafana_chronological_events;
" || {
    print_error "Failed to query chronological events view"
    exit 1
}

print_success "Chronological events view is working"

print_status "Checking Grafana dashboard provisioning..."

# Verify dashboard files exist
DASHBOARD_DIR="monitoring/grafana/dashboards/chronological-analysis"
if [ ! -d "$DASHBOARD_DIR" ]; then
    print_error "Dashboard directory not found: $DASHBOARD_DIR"
    exit 1
fi

if [ ! -f "$DASHBOARD_DIR/benchmark-chronological-logs.json" ]; then
    print_error "Main dashboard file not found"
    exit 1
fi

if [ ! -f "$DASHBOARD_DIR/enhanced-chronological-dashboard.json" ]; then
    print_error "Enhanced dashboard file not found"
    exit 1
fi

print_success "Dashboard files are present"

print_status "Restarting Grafana to load new dashboards..."

# Restart Grafana to pick up new dashboards
docker-compose restart grafana
sleep 15

print_status "Validating Grafana connection..."

# Wait for Grafana to be ready
GRAFANA_URL="http://localhost:3000"
MAX_ATTEMPTS=30
ATTEMPT=1

while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
    if curl -s -f "$GRAFANA_URL/api/health" > /dev/null 2>&1; then
        print_success "Grafana is ready"
        break
    fi
    
    if [ $ATTEMPT -eq $MAX_ATTEMPTS ]; then
        print_error "Grafana failed to start after $MAX_ATTEMPTS attempts"
        exit 1
    fi
    
    print_status "Waiting for Grafana... (attempt $ATTEMPT/$MAX_ATTEMPTS)"
    sleep 2
    ATTEMPT=$((ATTEMPT + 1))
done

print_status "Testing database connection from Grafana..."

# Test PostgreSQL datasource
docker-compose exec grafana curl -s -u admin:admin \
    "$GRAFANA_URL/api/datasources/name/PostgreSQL-Benchmarks" > /dev/null || {
    print_warning "PostgreSQL datasource may not be configured correctly"
}

print_status "Running dashboard validation tests..."

# Run the test suite for chronological events
docker-compose run --rm web-test pytest apps/main/tests/test_chronological_events_view.py -v || {
    print_warning "Some tests failed, but the setup may still work"
}

print_success "Setup completed successfully!"

echo ""
echo "📊 Logs Plugin Dashboard Setup Complete!"
echo ""
echo "🔗 Access your dashboards at:"
echo "   • Main Dashboard: $GRAFANA_URL/d/benchmark-chronological-logs"
echo "   • Enhanced Dashboard: $GRAFANA_URL/d/enhanced-chronological-analysis"
echo ""
echo "📋 Default credentials:"
echo "   • Username: admin"
echo "   • Password: admin"
echo ""
echo "🎯 Key Features:"
echo "   • Chronological event stream with intelligent filtering"
echo "   • Stage, tool call, and evaluation event decomposition"
echo "   • Performance-based log levels and categorization"
echo "   • Interactive filtering by run, agent, model, and event type"
echo ""
echo "📖 Usage Tips:"
echo "   1. Start by selecting a specific benchmark run to focus analysis"
echo "   2. Use event type filters to isolate stages, tool calls, or evaluations"
echo "   3. Apply performance filters to compare high vs low performing runs"
echo "   4. Click on log entries to see detailed JSON event data"
echo "   5. Use time range picker to analyze specific time periods"
echo ""
echo "🔍 For debugging:"
echo "   • Filter by 'error' or 'warn' log levels to find issues"
echo "   • Compare tool usage patterns between successful and failed runs"
echo "   • Analyze stage durations to identify bottlenecks"
echo "   • Review semantic evaluation details for quality insights"
echo ""

# Check if there's sample data
SAMPLE_DATA_COUNT=$(docker-compose exec db psql -U postgres -d mydb -t -c "
SELECT COUNT(*) FROM grafana_chronological_events;
" | tr -d ' ')

if [ "$SAMPLE_DATA_COUNT" -eq 0 ]; then
    echo "⚠️  No sample data found. To populate with test data:"
    echo "   docker-compose run --rm web python manage.py create_benchmark_scenarios_v3"
    echo "   docker-compose run --rm web python manage.py run_sample_benchmarks"
    echo ""
fi

print_success "Logs Plugin Dashboard is ready for use! 🎉"
